# NAHPi Complains API Integration Guide

## Overview

The NAHPi Complains API is a RESTful web service that provides comprehensive complaint management functionality for the National Association of Health Professionals Initiative (NAHPi). This guide will help you integrate with the API effectively.

## Base Information

- **Base URL**: `http://localhost:8000/api/` (Development)
- **API Version**: 1.0.0
- **Authentication**: JWT Bearer Token
- **Content Type**: `application/json`
- **Documentation**: `http://localhost:8000/api/docs/`

## Quick Start

### 1. Authentication

All protected endpoints require JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your_access_token>
```

### 2. Get Access Token

**Student Login:**
```bash
curl -X POST http://localhost:8000/api/auth/login/student/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your_password"
  }'
```

**Admin/Officer Login:**
```bash
curl -X POST http://localhost:8000/api/auth/login/admin/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your_password"
  }'
```

**Response:**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "role": "student",
    "is_verified": true
  }
}
```

### 3. Submit a Complaint

```bash
curl -X POST http://localhost:8000/api/complaints/ \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Broken projector in LH1",
    "description": "The projector in Lecture Hall 1 is not working properly",
    "complaint_type": "infrastructure",
    "department_code": "CSC",
    "incident_location": "Lecture Hall 1",
    "is_urgent": true
  }'
```

## Core API Endpoints

### Authentication Endpoints

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/auth/register/student/` | POST | Register student account | No |
| `/auth/login/student/` | POST | Student login | No |
| `/auth/login/admin/` | POST | Admin/Officer login | No |
| `/auth/token/refresh/` | POST | Refresh access token | No |
| `/auth/profile/` | GET, PUT | User profile | Yes |
| `/auth/change-password/` | POST | Change password | Yes |

### Complaint Management

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/complaints/` | GET, POST | List/Create complaints | Yes |
| `/complaints/{id}/` | GET, PUT | Complaint details | Yes |
| `/complaints/{id}/comments/` | GET, POST | Complaint comments | Yes |
| `/complaints/{id}/feedback/` | POST | Submit feedback | Yes |
| `/complaints/{id}/attachments/` | GET, POST | File attachments | Yes |
| `/complaints/{id}/assign/` | POST | Assign complaint | Admin |
| `/complaints/{id}/status/` | POST | Update status | Admin |

### Notifications

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/notifications/` | GET | List notifications | Yes |
| `/notifications/{id}/` | GET, PUT | Notification details | Yes |
| `/notifications/preferences/` | GET, PUT | User preferences | Yes |
| `/notifications/summary/` | GET | Notification summary | Yes |

### Analytics & Reports

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/complaints/analytics/` | GET | Complaint analytics | Admin |
| `/complaints/dashboard/admin/` | GET | Admin dashboard | Admin |
| `/complaints/dashboard/student/` | GET | Student dashboard | Student |
| `/notifications/admin/analytics/` | GET | Notification analytics | Admin |

## Data Models

### Complaint Object

```json
{
  "id": "uuid",
  "complaint_number": "NAH2024001234",
  "title": "Complaint title",
  "description": "Detailed description",
  "complaint_type": "academic|administrative|infrastructure|harassment|discrimination|other",
  "status": "submitted|under_review|in_progress|resolved|closed|rejected",
  "priority": "low|medium|high|urgent",
  "complainant": "user_id",
  "department": "department_id",
  "assigned_to": "user_id",
  "incident_location": "Location description",
  "incident_date": "2024-01-15",
  "is_urgent": false,
  "is_anonymous": false,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### User Object

```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "username": "username",
  "first_name": "John",
  "last_name": "Doe",
  "phone_number": "+2348012345678",
  "role": "student|admin|department_officer",
  "student_id": "NAH/2023/001234",
  "level": "100L",
  "department": "department_id",
  "is_verified": true,
  "is_active": true
}
```

### Notification Object

```json
{
  "id": "uuid",
  "title": "Notification title",
  "message": "Notification message",
  "notification_type": "complaint_submitted|status_changed|system_announcement",
  "priority": "low|medium|high",
  "is_read": false,
  "created_at": "2024-01-15T10:30:00Z",
  "action_url": "/complaints/uuid"
}
```

## Error Handling

### Standard Error Response

```json
{
  "error": "Error message",
  "details": {
    "field_name": ["Field-specific error message"]
  }
}
```

### Common HTTP Status Codes

- **200 OK**: Request successful
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server error

## Rate Limiting

- **Default**: 100 requests per minute per user
- **Authentication endpoints**: 10 requests per minute per IP
- **File uploads**: 20 requests per minute per user

## File Upload Guidelines

### Supported File Types
- Documents: PDF, DOC, DOCX, TXT
- Images: JPG, JPEG, PNG, GIF

### File Size Limits
- Maximum file size: 5MB per file
- Maximum files per complaint: 10 files

### Upload Example

```bash
curl -X POST http://localhost:8000/api/complaints/{id}/attachments/ \
  -H "Authorization: Bearer <access_token>" \
  -F "files=@document.pdf" \
  -F "files=@image.jpg"
```

## Pagination

List endpoints support pagination:

```json
{
  "count": 100,
  "next": "http://localhost:8000/api/complaints/?page=2",
  "previous": null,
  "results": [...]
}
```

Query parameters:
- `page`: Page number
- `page_size`: Items per page (default: 20, max: 100)

## Filtering and Search

### Complaint Filtering

```bash
# Filter by status
GET /api/complaints/?status=submitted

# Filter by type
GET /api/complaints/?type=academic

# Filter by department
GET /api/complaints/?department=CSC

# Search in title and description
GET /api/complaints/?search=projector

# Combine filters
GET /api/complaints/?status=submitted&type=infrastructure&search=broken
```

## Webhooks (Future Feature)

Webhook endpoints will be available for real-time notifications:

- Complaint status changes
- New complaint submissions
- Comment additions
- System announcements

## SDK and Libraries

### JavaScript/Node.js Example

```javascript
const axios = require('axios');

class NAHPiAPI {
  constructor(baseURL = 'http://localhost:8000/api/') {
    this.baseURL = baseURL;
    this.token = null;
  }

  async login(email, password, userType = 'student') {
    const response = await axios.post(`${this.baseURL}auth/login/${userType}/`, {
      email,
      password
    });
    this.token = response.data.access;
    return response.data;
  }

  async getComplaints() {
    const response = await axios.get(`${this.baseURL}complaints/`, {
      headers: { Authorization: `Bearer ${this.token}` }
    });
    return response.data;
  }

  async submitComplaint(complaintData) {
    const response = await axios.post(`${this.baseURL}complaints/`, complaintData, {
      headers: { Authorization: `Bearer ${this.token}` }
    });
    return response.data;
  }
}
```

### Python Example

```python
import requests

class NAHPiAPI:
    def __init__(self, base_url='http://localhost:8000/api/'):
        self.base_url = base_url
        self.token = None

    def login(self, email, password, user_type='student'):
        response = requests.post(f'{self.base_url}auth/login/{user_type}/', {
            'email': email,
            'password': password
        })
        data = response.json()
        self.token = data['access']
        return data

    def get_complaints(self):
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.get(f'{self.base_url}complaints/', headers=headers)
        return response.json()

    def submit_complaint(self, complaint_data):
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.post(f'{self.base_url}complaints/', 
                               json=complaint_data, headers=headers)
        return response.json()
```

## Support and Contact

- **Documentation**: http://localhost:8000/api/docs/
- **Support Email**: <EMAIL>
- **GitHub Issues**: [Repository URL]
- **API Status**: http://localhost:8000/api/health/

## Changelog

### Version 1.0.0 (Current)
- Initial API release
- Complete complaint management
- User authentication and authorization
- Notification system
- File upload support
- Analytics and reporting
