from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from django.urls import reverse
import json


@api_view(['GET'])
@permission_classes([AllowAny])
def api_overview(request):
    """
    API Overview - Main entry point for API documentation
    
    This endpoint provides a comprehensive overview of all available API endpoints
    in the NAHPi Complains system.
    """
    
    base_url = request.build_absolute_uri('/')[:-1]
    
    api_info = {
        "name": "NAHPi Complains API",
        "version": "1.0.0",
        "description": "REST API for the NAHPi Complaint Management System",
        "base_url": base_url,
        "documentation_url": f"{base_url}/api/docs/",
        "contact": {
            "name": "NAHPi IT Support",
            "email": "<EMAIL>"
        },
        "authentication": {
            "type": "JWT Bearer Token",
            "login_endpoint": f"{base_url}/api/auth/login/",
            "refresh_endpoint": f"{base_url}/api/auth/token/refresh/",
            "header_format": "Authorization: Bearer <token>"
        },
        "endpoints": {
            "authentication": {
                "base_url": f"{base_url}/api/auth/",
                "description": "User authentication and account management",
                "endpoints": [
                    {
                        "path": "/register/student/",
                        "method": "POST",
                        "description": "Register new student account",
                        "authentication": False
                    },
                    {
                        "path": "/login/student/",
                        "method": "POST", 
                        "description": "Student login",
                        "authentication": False
                    },
                    {
                        "path": "/login/admin/",
                        "method": "POST",
                        "description": "Admin/Department Officer login",
                        "authentication": False
                    },
                    {
                        "path": "/profile/",
                        "method": "GET, PUT",
                        "description": "Get/Update user profile",
                        "authentication": True
                    },
                    {
                        "path": "/change-password/",
                        "method": "POST",
                        "description": "Change user password",
                        "authentication": True
                    }
                ]
            },
            "complaints": {
                "base_url": f"{base_url}/api/complaints/",
                "description": "Complaint management and tracking",
                "endpoints": [
                    {
                        "path": "/",
                        "method": "GET, POST",
                        "description": "List complaints / Submit new complaint",
                        "authentication": True
                    },
                    {
                        "path": "/{id}/",
                        "method": "GET, PUT",
                        "description": "Get/Update complaint details",
                        "authentication": True
                    },
                    {
                        "path": "/{id}/comments/",
                        "method": "GET, POST",
                        "description": "List/Add complaint comments",
                        "authentication": True
                    },
                    {
                        "path": "/{id}/feedback/",
                        "method": "POST",
                        "description": "Submit complaint feedback",
                        "authentication": True
                    },
                    {
                        "path": "/{id}/attachments/",
                        "method": "GET, POST",
                        "description": "List/Upload complaint attachments",
                        "authentication": True
                    }
                ]
            },
            "notifications": {
                "base_url": f"{base_url}/api/notifications/",
                "description": "Notification management",
                "endpoints": [
                    {
                        "path": "/",
                        "method": "GET",
                        "description": "List user notifications",
                        "authentication": True
                    },
                    {
                        "path": "/preferences/",
                        "method": "GET, PUT",
                        "description": "Get/Update notification preferences",
                        "authentication": True
                    },
                    {
                        "path": "/summary/",
                        "method": "GET",
                        "description": "Get notification summary",
                        "authentication": True
                    }
                ]
            },
            "admin": {
                "base_url": f"{base_url}/api/",
                "description": "Admin-only endpoints",
                "authentication_required": "Admin role",
                "endpoints": [
                    {
                        "path": "/auth/admin/users/",
                        "method": "GET, POST",
                        "description": "Manage users"
                    },
                    {
                        "path": "/complaints/analytics/",
                        "method": "GET",
                        "description": "Complaint analytics"
                    },
                    {
                        "path": "/notifications/admin/bulk/",
                        "method": "POST",
                        "description": "Send bulk notifications"
                    }
                ]
            }
        },
        "response_format": {
            "success": {
                "status": "success",
                "data": "Response data",
                "message": "Success message (optional)"
            },
            "error": {
                "status": "error",
                "error": "Error message",
                "details": "Additional error details (optional)"
            }
        },
        "status_codes": {
            "200": "OK - Request successful",
            "201": "Created - Resource created successfully",
            "400": "Bad Request - Invalid request data",
            "401": "Unauthorized - Authentication required",
            "403": "Forbidden - Insufficient permissions",
            "404": "Not Found - Resource not found",
            "500": "Internal Server Error - Server error"
        }
    }
    
    return Response(api_info, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([AllowAny])
def authentication_docs(request):
    """
    Authentication Documentation
    
    Detailed documentation for authentication endpoints and JWT token usage.
    """
    
    base_url = request.build_absolute_uri('/')[:-1]
    
    auth_docs = {
        "title": "Authentication Documentation",
        "description": "How to authenticate and manage user accounts",
        "jwt_authentication": {
            "description": "The API uses JWT (JSON Web Tokens) for authentication",
            "flow": [
                "1. Register or login to get access and refresh tokens",
                "2. Include access token in Authorization header for protected endpoints",
                "3. Use refresh token to get new access token when it expires",
                "4. Access tokens expire in 60 minutes, refresh tokens in 7 days"
            ],
            "header_format": "Authorization: Bearer <access_token>"
        },
        "endpoints": {
            "student_registration": {
                "url": f"{base_url}/api/auth/register/student/",
                "method": "POST",
                "description": "Register a new student account",
                "required_fields": [
                    "email", "username", "password", "password_confirm",
                    "first_name", "last_name", "phone_number", "student_id",
                    "level", "department_code"
                ],
                "example_request": {
                    "email": "<EMAIL>",
                    "username": "student123",
                    "password": "SecurePass123!",
                    "password_confirm": "SecurePass123!",
                    "first_name": "John",
                    "last_name": "Doe",
                    "phone_number": "+*************",
                    "student_id": "NAH/2023/001234",
                    "level": "200L",
                    "department_code": "CSC"
                },
                "example_response": {
                    "message": "Registration successful. Please verify your email.",
                    "user": {
                        "id": "uuid",
                        "email": "<EMAIL>",
                        "username": "student123",
                        "role": "student"
                    }
                }
            },
            "student_login": {
                "url": f"{base_url}/api/auth/login/student/",
                "method": "POST",
                "description": "Login for students",
                "required_fields": ["email", "password"],
                "example_request": {
                    "email": "<EMAIL>",
                    "password": "SecurePass123!"
                },
                "example_response": {
                    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                    "user": {
                        "id": "uuid",
                        "email": "<EMAIL>",
                        "role": "student",
                        "is_verified": True
                    }
                }
            },
            "admin_login": {
                "url": f"{base_url}/api/auth/login/admin/",
                "method": "POST",
                "description": "Login for admin and department officers",
                "required_fields": ["email", "password"],
                "example_request": {
                    "email": "<EMAIL>",
                    "password": "AdminPass123!"
                }
            },
            "token_refresh": {
                "url": f"{base_url}/api/auth/token/refresh/",
                "method": "POST",
                "description": "Refresh access token using refresh token",
                "required_fields": ["refresh"],
                "example_request": {
                    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
                },
                "example_response": {
                    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
                }
            }
        },
        "error_responses": {
            "invalid_credentials": {
                "status": 401,
                "error": "Invalid credentials"
            },
            "account_not_verified": {
                "status": 403,
                "error": "Account not verified. Please verify your email."
            },
            "validation_error": {
                "status": 400,
                "error": "Validation failed",
                "details": {
                    "email": ["This field is required."],
                    "password": ["Password too weak."]
                }
            }
        }
    }
    
    return Response(auth_docs, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([AllowAny])
def complaints_docs(request):
    """
    Complaints API Documentation
    
    Detailed documentation for complaint management endpoints.
    """
    
    base_url = request.build_absolute_uri('/')[:-1]
    
    complaints_docs = {
        "title": "Complaints API Documentation",
        "description": "How to manage complaints through the API",
        "base_url": f"{base_url}/api/complaints/",
        "authentication": "Required - JWT Bearer token",
        "endpoints": {
            "list_create_complaints": {
                "url": f"{base_url}/api/complaints/",
                "methods": ["GET", "POST"],
                "description": "List complaints or create new complaint",
                "get_parameters": [
                    "status - Filter by complaint status",
                    "type - Filter by complaint type", 
                    "department - Filter by department code",
                    "priority - Filter by priority level",
                    "search - Search in title and description"
                ],
                "post_fields": [
                    "title", "description", "complaint_type", "department_code",
                    "incident_location", "incident_date", "is_urgent", "is_anonymous"
                ],
                "example_create": {
                    "title": "Broken projector in LH1",
                    "description": "The projector in Lecture Hall 1 is not working",
                    "complaint_type": "infrastructure",
                    "department_code": "CSC",
                    "incident_location": "Lecture Hall 1",
                    "is_urgent": True
                }
            },
            "complaint_detail": {
                "url": f"{base_url}/api/complaints/{{id}}/",
                "methods": ["GET", "PUT"],
                "description": "Get or update complaint details",
                "permissions": {
                    "GET": "Complaint owner or staff",
                    "PUT": "Admin or department officer only"
                }
            },
            "complaint_comments": {
                "url": f"{base_url}/api/complaints/{{id}}/comments/",
                "methods": ["GET", "POST"],
                "description": "List or add complaint comments",
                "note": "Students cannot see internal comments"
            },
            "file_attachments": {
                "url": f"{base_url}/api/complaints/{{id}}/attachments/",
                "methods": ["GET", "POST"],
                "description": "List or upload file attachments",
                "supported_formats": ["PDF", "DOC", "DOCX", "JPG", "PNG", "GIF", "TXT"],
                "max_file_size": "5MB per file"
            }
        },
        "complaint_statuses": [
            "submitted", "under_review", "in_progress", 
            "resolved", "closed", "rejected"
        ],
        "complaint_types": [
            "academic", "administrative", "infrastructure", 
            "harassment", "discrimination", "other"
        ],
        "priority_levels": ["low", "medium", "high", "urgent"]
    }
    
    return Response(complaints_docs, status=status.HTTP_200_OK)
