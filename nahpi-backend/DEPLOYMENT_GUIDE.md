# NAHPi Complains Backend - Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the NAHPi Complains backend system to production environments.

## Pre-Deployment Checklist

### ✅ Code Quality & Testing
- [ ] All tests pass (`python run_tests.py`)
- [ ] Code coverage > 80%
- [ ] No security vulnerabilities (`python manage.py check --deploy`)
- [ ] All migrations created and applied
- [ ] Static files collected
- [ ] Environment variables configured

### ✅ Database Setup
- [ ] Production database configured (PostgreSQL recommended)
- [ ] Database migrations applied
- [ ] Initial data loaded (departments, admin users)
- [ ] Database backups configured
- [ ] Connection pooling configured

### ✅ Security Configuration
- [ ] SECRET_KEY changed from default
- [ ] DEBUG = False in production
- [ ] ALLOWED_HOSTS configured
- [ ] CORS settings configured
- [ ] SSL/TLS certificates installed
- [ ] Security headers configured

### ✅ Infrastructure
- [ ] Web server configured (Nginx/Apache)
- [ ] Application server configured (Gunicorn/uWSGI)
- [ ] Load balancer configured (if needed)
- [ ] CDN configured for static files
- [ ] Monitoring and logging setup

## Environment Configuration

### Production Settings

Create a production settings file (`nahpi_complains/settings/production.py`):

```python
from .base import *
import os

# Security
DEBUG = False
SECRET_KEY = os.environ.get('SECRET_KEY')
ALLOWED_HOSTS = ['your-domain.com', 'www.your-domain.com']

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('DB_NAME'),
        'USER': os.environ.get('DB_USER'),
        'PASSWORD': os.environ.get('DB_PASSWORD'),
        'HOST': os.environ.get('DB_HOST', 'localhost'),
        'PORT': os.environ.get('DB_PORT', '5432'),
        'OPTIONS': {
            'sslmode': 'require',
        },
    }
}

# Email Configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', 587))
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')

# Static and Media Files
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Security Headers
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# CORS Configuration
CORS_ALLOWED_ORIGINS = [
    "https://your-frontend-domain.com",
]

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/nahpi/django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### Environment Variables

Create a `.env` file for production:

```bash
# Django Settings
SECRET_KEY=your-super-secret-key-here
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# Database
DB_NAME=nahpi_complains_prod
DB_USER=nahpi_user
DB_PASSWORD=secure_password
DB_HOST=localhost
DB_PORT=5432

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=email_password

# File Upload
MAX_FILE_SIZE=5242880  # 5MB in bytes

# JWT Settings
JWT_ACCESS_TOKEN_LIFETIME=60  # minutes
JWT_REFRESH_TOKEN_LIFETIME=10080  # 7 days in minutes

# External Services
SMS_API_KEY=your_sms_api_key
PUSH_NOTIFICATION_KEY=your_push_key
```

## Server Configuration

### Nginx Configuration

Create `/etc/nginx/sites-available/nahpi-complains`:

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload";

    # Static files
    location /static/ {
        alias /path/to/nahpi-backend/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Media files
    location /media/ {
        alias /path/to/nahpi-backend/media/;
        expires 1y;
        add_header Cache-Control "public";
    }

    # API endpoints
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Increase timeout for file uploads
        proxy_read_timeout 300;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Gunicorn Configuration

Create `gunicorn.conf.py`:

```python
bind = "127.0.0.1:8000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
user = "nahpi"
group = "nahpi"
tmp_upload_dir = None
secure_scheme_headers = {
    'X-FORWARDED-PROTOCOL': 'ssl',
    'X-FORWARDED-PROTO': 'https',
    'X-FORWARDED-SSL': 'on'
}
```

### Systemd Service

Create `/etc/systemd/system/nahpi-complains.service`:

```ini
[Unit]
Description=NAHPi Complains Django Application
After=network.target

[Service]
Type=notify
User=nahpi
Group=nahpi
WorkingDirectory=/path/to/nahpi-backend
Environment=DJANGO_SETTINGS_MODULE=nahpi_complains.settings.production
ExecStart=/path/to/venv/bin/gunicorn nahpi_complains.wsgi:application -c gunicorn.conf.py
ExecReload=/bin/kill -s HUP $MAINPID
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
```

## Database Setup

### PostgreSQL Installation and Configuration

```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Create database and user
sudo -u postgres psql
CREATE DATABASE nahpi_complains_prod;
CREATE USER nahpi_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE nahpi_complains_prod TO nahpi_user;
ALTER USER nahpi_user CREATEDB;
\q

# Configure PostgreSQL
sudo nano /etc/postgresql/13/main/postgresql.conf
# Set: shared_preload_libraries = 'pg_stat_statements'

sudo nano /etc/postgresql/13/main/pg_hba.conf
# Add: local   nahpi_complains_prod   nahpi_user   md5

sudo systemctl restart postgresql
```

### Run Migrations

```bash
# Activate virtual environment
source venv/bin/activate

# Set environment
export DJANGO_SETTINGS_MODULE=nahpi_complains.settings.production

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic --noinput

# Load initial data
python manage.py loaddata initial_departments.json
```

## Monitoring and Logging

### Log Configuration

Create log directories:
```bash
sudo mkdir -p /var/log/nahpi
sudo chown nahpi:nahpi /var/log/nahpi
```

### Health Check Endpoint

Add to `nahpi_complains/urls.py`:
```python
from django.http import JsonResponse
from django.db import connection

def health_check(request):
    try:
        # Check database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        
        return JsonResponse({
            'status': 'healthy',
            'database': 'connected',
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        return JsonResponse({
            'status': 'unhealthy',
            'error': str(e)
        }, status=500)

urlpatterns = [
    # ... existing patterns
    path('health/', health_check, name='health_check'),
]
```

## Backup Strategy

### Database Backups

Create backup script `/home/<USER>/backup_db.sh`:
```bash
#!/bin/bash
BACKUP_DIR="/backups/nahpi"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="nahpi_complains_prod"

mkdir -p $BACKUP_DIR

# Create database backup
pg_dump -h localhost -U nahpi_user $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Keep only last 30 days of backups
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete

echo "Database backup completed: db_backup_$DATE.sql.gz"
```

Add to crontab:
```bash
# Daily database backup at 2 AM
0 2 * * * /home/<USER>/backup_db.sh
```

## Performance Optimization

### Database Optimization

```sql
-- Create indexes for better performance
CREATE INDEX idx_complaints_status ON complaints_complaint(status);
CREATE INDEX idx_complaints_created_at ON complaints_complaint(created_at);
CREATE INDEX idx_complaints_department ON complaints_complaint(department_id);
CREATE INDEX idx_notifications_recipient ON notifications_notification(recipient_id);
CREATE INDEX idx_notifications_created_at ON notifications_notification(created_at);
```

### Caching Configuration

Add to settings:
```python
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Session configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'
```

## Security Hardening

### Firewall Configuration

```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### SSL Certificate (Let's Encrypt)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Deployment Commands

### Initial Deployment

```bash
# 1. Clone repository
git clone https://github.com/your-org/nahpi-backend.git
cd nahpi-backend

# 2. Create virtual environment
python3 -m venv venv
source venv/bin/activate

# 3. Install dependencies
pip install -r requirements.txt

# 4. Configure environment
cp .env.example .env
# Edit .env with production values

# 5. Run migrations
python manage.py migrate

# 6. Collect static files
python manage.py collectstatic

# 7. Create superuser
python manage.py createsuperuser

# 8. Start services
sudo systemctl start nahpi-complains
sudo systemctl enable nahpi-complains
sudo systemctl restart nginx
```

### Updates and Maintenance

```bash
# Update deployment
git pull origin main
source venv/bin/activate
pip install -r requirements.txt
python manage.py migrate
python manage.py collectstatic --noinput
sudo systemctl restart nahpi-complains
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check PostgreSQL service status
   - Verify database credentials
   - Check firewall settings

2. **Static Files Not Loading**
   - Run `python manage.py collectstatic`
   - Check Nginx configuration
   - Verify file permissions

3. **Email Not Sending**
   - Check SMTP settings
   - Verify email credentials
   - Check firewall for SMTP ports

4. **High Memory Usage**
   - Adjust Gunicorn worker count
   - Enable database connection pooling
   - Implement caching

### Log Locations

- Django logs: `/var/log/nahpi/django.log`
- Nginx logs: `/var/log/nginx/access.log`, `/var/log/nginx/error.log`
- PostgreSQL logs: `/var/log/postgresql/postgresql-13-main.log`
- System logs: `journalctl -u nahpi-complains`

## Support and Maintenance

### Regular Maintenance Tasks

- [ ] Weekly: Review error logs
- [ ] Monthly: Update dependencies
- [ ] Monthly: Review database performance
- [ ] Quarterly: Security audit
- [ ] Quarterly: Backup restoration test

### Contact Information

- **Technical Support**: <EMAIL>
- **Emergency Contact**: +234-xxx-xxx-xxxx
- **Documentation**: https://docs.nahpi.edu.ng
