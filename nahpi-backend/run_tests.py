#!/usr/bin/env python
"""
Comprehensive test runner for NAHPi Complains Backend

This script runs all tests, generates coverage reports, and performs
quality assurance checks.
"""

import os
import sys
import subprocess
import django
from django.conf import settings
from django.test.utils import get_runner
from django.core.management import execute_from_command_line


def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nahpi_backend.settings')
    django.setup()


def run_tests():
    """Run all tests with coverage"""
    print("=" * 60)
    print("RUNNING COMPREHENSIVE TEST SUITE")
    print("=" * 60)
    
    # Test commands to run
    test_commands = [
        # Run all tests with coverage
        ['python', 'manage.py', 'test', '--verbosity=2'],
        
        # Run specific app tests
        ['python', 'manage.py', 'test', 'accounts', '--verbosity=2'],
        ['python', 'manage.py', 'test', 'complaints', '--verbosity=2'],
        ['python', 'manage.py', 'test', 'notifications', '--verbosity=2'],
    ]
    
    results = []
    
    for i, command in enumerate(test_commands, 1):
        print(f"\n{'-' * 40}")
        print(f"Running Test Suite {i}/{len(test_commands)}")
        print(f"Command: {' '.join(command)}")
        print(f"{'-' * 40}")
        
        try:
            result = subprocess.run(command, capture_output=True, text=True)
            results.append({
                'command': ' '.join(command),
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            })
            
            if result.returncode == 0:
                print("✅ PASSED")
            else:
                print("❌ FAILED")
                print("STDOUT:", result.stdout)
                print("STDERR:", result.stderr)
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
            results.append({
                'command': ' '.join(command),
                'returncode': -1,
                'error': str(e)
            })
    
    return results


def run_quality_checks():
    """Run code quality checks"""
    print("\n" + "=" * 60)
    print("RUNNING QUALITY ASSURANCE CHECKS")
    print("=" * 60)
    
    quality_commands = [
        # Check for migrations
        ['python', 'manage.py', 'makemigrations', '--check', '--dry-run'],
        
        # Check Django system
        ['python', 'manage.py', 'check'],
        
        # Check for security issues
        ['python', 'manage.py', 'check', '--deploy'],
    ]
    
    results = []
    
    for i, command in enumerate(quality_commands, 1):
        print(f"\n{'-' * 40}")
        print(f"Running Quality Check {i}/{len(quality_commands)}")
        print(f"Command: {' '.join(command)}")
        print(f"{'-' * 40}")
        
        try:
            result = subprocess.run(command, capture_output=True, text=True)
            results.append({
                'command': ' '.join(command),
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            })
            
            if result.returncode == 0:
                print("✅ PASSED")
                if result.stdout:
                    print("Output:", result.stdout)
            else:
                print("❌ FAILED")
                print("STDOUT:", result.stdout)
                print("STDERR:", result.stderr)
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
            results.append({
                'command': ' '.join(command),
                'returncode': -1,
                'error': str(e)
            })
    
    return results


def generate_test_report(test_results, quality_results):
    """Generate comprehensive test report"""
    print("\n" + "=" * 60)
    print("TEST REPORT SUMMARY")
    print("=" * 60)
    
    # Test Results Summary
    print("\n📋 TEST RESULTS:")
    print("-" * 30)
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results if result['returncode'] == 0)
    failed_tests = total_tests - passed_tests
    
    print(f"Total Test Suites: {total_tests}")
    print(f"Passed: {passed_tests} ✅")
    print(f"Failed: {failed_tests} ❌")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if failed_tests > 0:
        print("\n❌ FAILED TESTS:")
        for result in test_results:
            if result['returncode'] != 0:
                print(f"  - {result['command']}")
    
    # Quality Check Summary
    print("\n🔍 QUALITY CHECKS:")
    print("-" * 30)
    
    total_checks = len(quality_results)
    passed_checks = sum(1 for result in quality_results if result['returncode'] == 0)
    failed_checks = total_checks - passed_checks
    
    print(f"Total Quality Checks: {total_checks}")
    print(f"Passed: {passed_checks} ✅")
    print(f"Failed: {failed_checks} ❌")
    
    if failed_checks > 0:
        print("\n❌ FAILED QUALITY CHECKS:")
        for result in quality_results:
            if result['returncode'] != 0:
                print(f"  - {result['command']}")
    
    # Overall Status
    print("\n" + "=" * 60)
    if failed_tests == 0 and failed_checks == 0:
        print("🎉 ALL TESTS AND QUALITY CHECKS PASSED!")
        print("✅ System is ready for deployment")
    else:
        print("⚠️  SOME TESTS OR QUALITY CHECKS FAILED")
        print("❌ Please review and fix issues before deployment")
    print("=" * 60)
    
    return {
        'tests': {
            'total': total_tests,
            'passed': passed_tests,
            'failed': failed_tests,
            'success_rate': (passed_tests/total_tests)*100
        },
        'quality': {
            'total': total_checks,
            'passed': passed_checks,
            'failed': failed_checks
        },
        'overall_success': failed_tests == 0 and failed_checks == 0
    }


def run_api_endpoint_tests():
    """Test API endpoints are accessible"""
    print("\n" + "=" * 60)
    print("TESTING API ENDPOINTS")
    print("=" * 60)
    
    # Start Django development server in background for testing
    print("Starting Django development server for endpoint testing...")
    
    try:
        # Import Django test client
        from django.test import Client
        client = Client()
        
        # Test endpoints
        endpoints_to_test = [
            {'url': '/api/docs/', 'method': 'GET', 'name': 'API Documentation'},
            {'url': '/api/auth/check-email/', 'method': 'POST', 'name': 'Check Email Endpoint'},
            {'url': '/api/auth/departments/', 'method': 'GET', 'name': 'Departments List'},
        ]
        
        results = []
        
        for endpoint in endpoints_to_test:
            try:
                if endpoint['method'] == 'GET':
                    response = client.get(endpoint['url'])
                elif endpoint['method'] == 'POST':
                    response = client.post(endpoint['url'], {})
                
                status_code = response.status_code
                success = status_code in [200, 201, 400, 401, 403]  # Expected status codes
                
                print(f"  {endpoint['name']}: {status_code} {'✅' if success else '❌'}")
                
                results.append({
                    'name': endpoint['name'],
                    'url': endpoint['url'],
                    'status_code': status_code,
                    'success': success
                })
                
            except Exception as e:
                print(f"  {endpoint['name']}: ERROR - {e} ❌")
                results.append({
                    'name': endpoint['name'],
                    'url': endpoint['url'],
                    'error': str(e),
                    'success': False
                })
        
        return results
        
    except Exception as e:
        print(f"Error testing API endpoints: {e}")
        return []


def main():
    """Main test runner function"""
    print("NAHPi Complains Backend - Comprehensive Test Suite")
    print("=" * 60)
    
    # Setup Django
    setup_django()
    
    # Run tests
    test_results = run_tests()
    
    # Run quality checks
    quality_results = run_quality_checks()
    
    # Test API endpoints
    api_results = run_api_endpoint_tests()
    
    # Generate report
    report = generate_test_report(test_results, quality_results)
    
    # API endpoint summary
    if api_results:
        print(f"\n🌐 API ENDPOINT TESTS:")
        print("-" * 30)
        successful_endpoints = sum(1 for result in api_results if result.get('success', False))
        total_endpoints = len(api_results)
        print(f"Accessible Endpoints: {successful_endpoints}/{total_endpoints}")
    
    # Exit with appropriate code
    if report['overall_success']:
        print("\n🎉 All tests completed successfully!")
        sys.exit(0)
    else:
        print("\n⚠️  Some tests failed. Please review the output above.")
        sys.exit(1)


if __name__ == '__main__':
    main()
