from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils import timezone
from datetime import timedelta
from .models import Complaint, ComplaintFeedback
from accounts.models import User


def send_complaint_notification(complaint, notification_type, recipient=None):
    """Send complaint-related notifications"""
    
    if notification_type == 'submitted':
        # Notify department officers
        department_officers = User.objects.filter(
            role='department_officer',
            department=complaint.department,
            is_active=True
        )
        
        for officer in department_officers:
            subject = f'New Complaint Submitted - {complaint.complaint_number}'
            message = f"""
            A new complaint has been submitted to your department.
            
            Complaint Number: {complaint.complaint_number}
            Title: {complaint.title}
            Type: {complaint.get_complaint_type_display()}
            Priority: {complaint.get_priority_display()}
            Submitted by: {complaint.complainant.full_name}
            
            Please review and take appropriate action.
            """
            
            try:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[officer.email],
                    fail_silently=True
                )
            except Exception as e:
                print(f"Failed to send notification to {officer.email}: {e}")
    
    elif notification_type == 'status_updated':
        # Notify complainant
        subject = f'Complaint Status Updated - {complaint.complaint_number}'
        message = f"""
        Your complaint status has been updated.
        
        Complaint Number: {complaint.complaint_number}
        Title: {complaint.title}
        New Status: {complaint.get_status_display()}
        
        You can view the details by logging into your account.
        """
        
        try:
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.EMAIL_HOST_USER,
                recipient_list=[complaint.complainant.email],
                fail_silently=True
            )
        except Exception as e:
            print(f"Failed to send notification to {complaint.complainant.email}: {e}")
    
    elif notification_type == 'resolved':
        # Notify complainant about resolution
        subject = f'Complaint Resolved - {complaint.complaint_number}'
        message = f"""
        Your complaint has been resolved.
        
        Complaint Number: {complaint.complaint_number}
        Title: {complaint.title}
        Resolution: {complaint.resolution}
        Resolved by: {complaint.resolved_by.full_name if complaint.resolved_by else 'System'}
        
        Please provide feedback on your experience.
        """
        
        try:
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.EMAIL_HOST_USER,
                recipient_list=[complaint.complainant.email],
                fail_silently=True
            )
        except Exception as e:
            print(f"Failed to send notification to {complaint.complainant.email}: {e}")


def send_daily_digest_to_department_officers():
    """Send daily digest emails to department officers"""
    from django.db.models import Count, Q
    
    # Get all active department officers
    officers = User.objects.filter(
        role='department_officer',
        is_active=True,
        department__isnull=False
    ).select_related('department')
    
    for officer in officers:
        # Get complaints for this officer's department
        complaints = Complaint.objects.filter(department=officer.department)
        
        # Calculate statistics
        total_complaints = complaints.count()
        pending_complaints = complaints.filter(
            status__in=['submitted', 'under_review', 'in_progress']
        ).count()
        overdue_complaints = complaints.filter(
            status__in=['submitted', 'under_review', 'in_progress'],
            created_at__lt=timezone.now() - timedelta(days=7)
        ).count()
        
        # Get new complaints from yesterday
        yesterday = timezone.now() - timedelta(days=1)
        new_complaints = complaints.filter(created_at__gte=yesterday)
        
        if new_complaints.exists() or overdue_complaints > 0:
            subject = f'Daily Complaint Digest - {officer.department.name}'
            message = f"""
            Daily Complaint Digest for {officer.department.name}
            
            Summary:
            - Total Complaints: {total_complaints}
            - Pending Complaints: {pending_complaints}
            - Overdue Complaints: {overdue_complaints}
            - New Complaints (Yesterday): {new_complaints.count()}
            
            """
            
            if new_complaints.exists():
                message += "\nNew Complaints:\n"
                for complaint in new_complaints:
                    message += f"- {complaint.complaint_number}: {complaint.title}\n"
            
            if overdue_complaints > 0:
                message += f"\nPlease review the {overdue_complaints} overdue complaints.\n"
            
            try:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[officer.email],
                    fail_silently=True
                )
            except Exception as e:
                print(f"Failed to send daily digest to {officer.email}: {e}")


def generate_complaint_report(filters=None):
    """Generate comprehensive complaint report"""
    from django.db.models import Count, Avg, F
    
    # Apply filters
    queryset = Complaint.objects.all()
    if filters:
        if filters.get('department'):
            queryset = queryset.filter(department__code=filters['department'])
        if filters.get('status'):
            queryset = queryset.filter(status=filters['status'])
        if filters.get('type'):
            queryset = queryset.filter(complaint_type=filters['type'])
        if filters.get('date_from'):
            queryset = queryset.filter(created_at__gte=filters['date_from'])
        if filters.get('date_to'):
            queryset = queryset.filter(created_at__lte=filters['date_to'])
    
    # Generate report data
    report = {
        'summary': {
            'total_complaints': queryset.count(),
            'by_status': list(queryset.values('status').annotate(count=Count('id'))),
            'by_type': list(queryset.values('complaint_type').annotate(count=Count('id'))),
            'by_department': list(
                queryset.values('department__name', 'department__code')
                .annotate(count=Count('id'))
                .order_by('-count')
            ),
        },
        'performance': {
            'average_resolution_time': 0,
            'satisfaction_rating': 0,
            'overdue_complaints': queryset.filter(
                status__in=['submitted', 'under_review', 'in_progress'],
                created_at__lt=timezone.now() - timedelta(days=7)
            ).count(),
        },
        'trends': [],
        'filters_applied': filters or {},
        'generated_at': timezone.now().isoformat(),
    }
    
    # Calculate average resolution time
    resolved_queryset = queryset.filter(resolved_at__isnull=False)
    if resolved_queryset.exists():
        avg_resolution_time = resolved_queryset.aggregate(
            avg_time=Avg(F('resolved_at') - F('created_at'))
        )['avg_time']
        if avg_resolution_time:
            report['performance']['average_resolution_time'] = avg_resolution_time.days
    
    # Calculate satisfaction rating
    feedback_queryset = ComplaintFeedback.objects.filter(complaint__in=queryset)
    if feedback_queryset.exists():
        avg_rating = feedback_queryset.aggregate(avg_rating=Avg('satisfaction_rating'))['avg_rating']
        report['performance']['satisfaction_rating'] = round(avg_rating or 0, 2)
    
    # Generate monthly trends
    for i in range(12):
        month_start = timezone.now().replace(day=1) - timedelta(days=30*i)
        month_end = month_start.replace(day=28) + timedelta(days=4)
        month_end = month_end - timedelta(days=month_end.day)
        
        month_complaints = queryset.filter(
            created_at__gte=month_start,
            created_at__lte=month_end
        ).count()
        
        month_resolved = queryset.filter(
            resolved_at__gte=month_start,
            resolved_at__lte=month_end
        ).count()
        
        report['trends'].append({
            'month': month_start.strftime('%Y-%m'),
            'complaints': month_complaints,
            'resolved': month_resolved
        })
    
    report['trends'].reverse()
    
    return report


def export_complaints_to_csv(queryset=None):
    """Export complaints to CSV format"""
    import csv
    import io
    
    if queryset is None:
        queryset = Complaint.objects.all()
    
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow([
        'Complaint Number', 'Title', 'Type', 'Status', 'Priority',
        'Complainant', 'Department', 'Assigned To', 'Created At',
        'Resolved At', 'Resolution', 'Is Urgent', 'Is Anonymous'
    ])
    
    # Write complaint data
    complaints = queryset.select_related(
        'complainant', 'department', 'assigned_to', 'resolved_by'
    )
    
    for complaint in complaints:
        writer.writerow([
            complaint.complaint_number,
            complaint.title,
            complaint.get_complaint_type_display(),
            complaint.get_status_display(),
            complaint.get_priority_display(),
            complaint.complainant.full_name,
            complaint.department.name,
            complaint.assigned_to.full_name if complaint.assigned_to else '',
            complaint.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            complaint.resolved_at.strftime('%Y-%m-%d %H:%M:%S') if complaint.resolved_at else '',
            complaint.resolution or '',
            complaint.is_urgent,
            complaint.is_anonymous
        ])
    
    return output.getvalue()


def cleanup_old_complaints(days_old=365):
    """Archive or cleanup old resolved complaints"""
    cutoff_date = timezone.now() - timedelta(days=days_old)
    
    # Find old resolved complaints
    old_complaints = Complaint.objects.filter(
        status__in=['resolved', 'closed'],
        resolved_at__lt=cutoff_date
    )
    
    count = old_complaints.count()
    
    # In a real system, you might archive these instead of deleting
    # For now, we'll just return the count without deleting
    return count


def get_complaint_statistics(department=None):
    """Get complaint statistics for a department or overall"""
    from django.db.models import Count, Avg, F
    
    queryset = Complaint.objects.all()
    if department:
        queryset = queryset.filter(department=department)
    
    stats = {
        'total': queryset.count(),
        'by_status': dict(queryset.values_list('status').annotate(Count('id'))),
        'by_type': dict(queryset.values_list('complaint_type').annotate(Count('id'))),
        'by_priority': dict(queryset.values_list('priority').annotate(Count('id'))),
        'average_resolution_time': 0,
        'satisfaction_rating': 0,
    }
    
    # Calculate average resolution time
    resolved_queryset = queryset.filter(resolved_at__isnull=False)
    if resolved_queryset.exists():
        avg_resolution_time = resolved_queryset.aggregate(
            avg_time=Avg(F('resolved_at') - F('created_at'))
        )['avg_time']
        if avg_resolution_time:
            stats['average_resolution_time'] = avg_resolution_time.days
    
    # Calculate satisfaction rating
    feedback_queryset = ComplaintFeedback.objects.filter(complaint__in=queryset)
    if feedback_queryset.exists():
        avg_rating = feedback_queryset.aggregate(avg_rating=Avg('satisfaction_rating'))['avg_rating']
        stats['satisfaction_rating'] = round(avg_rating or 0, 2)
    
    return stats
