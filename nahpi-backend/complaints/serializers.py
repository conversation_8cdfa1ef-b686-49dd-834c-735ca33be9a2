from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    Co<PERSON><PERSON><PERSON>, ComplaintAttachment, ComplaintStatusHistory, 
    ComplaintFeedback, ComplaintComment
)
from accounts.models import Department

User = get_user_model()


class ComplaintAttachmentSerializer(serializers.ModelSerializer):
    """Serializer for complaint attachments"""
    
    class Meta:
        model = ComplaintAttachment
        fields = ['id', 'file', 'original_filename', 'file_size', 'content_type', 'uploaded_at']
        read_only_fields = ['id', 'original_filename', 'file_size', 'content_type', 'uploaded_at']


class ComplaintCommentSerializer(serializers.ModelSerializer):
    """Serializer for complaint comments"""
    author_name = serializers.CharField(source='author.full_name', read_only=True)
    author_role = serializers.CharField(source='author.role', read_only=True)
    
    class Meta:
        model = ComplaintComment
        fields = [
            'id', 'content', 'author', 'author_name', 'author_role',
            'is_internal', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'author', 'created_at', 'updated_at']


class ComplaintStatusHistorySerializer(serializers.ModelSerializer):
    """Serializer for complaint status history"""
    changed_by_name = serializers.CharField(source='changed_by.full_name', read_only=True)
    
    class Meta:
        model = ComplaintStatusHistory
        fields = [
            'id', 'previous_status', 'new_status', 'changed_by', 
            'changed_by_name', 'comment', 'timestamp'
        ]
        read_only_fields = ['id', 'timestamp']


class ComplaintFeedbackSerializer(serializers.ModelSerializer):
    """Serializer for complaint feedback"""
    
    class Meta:
        model = ComplaintFeedback
        fields = [
            'id', 'satisfaction_rating', 'feedback_text', 'response_time_rating',
            'resolution_quality_rating', 'staff_helpfulness_rating', 'suggestions',
            'would_recommend', 'average_rating', 'created_at'
        ]
        read_only_fields = ['id', 'average_rating', 'created_at']


class ComplaintListSerializer(serializers.ModelSerializer):
    """Serializer for listing complaints"""
    complainant_name = serializers.CharField(source='complainant.full_name', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.full_name', read_only=True)
    days_since_submission = serializers.IntegerField(read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    attachment_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Complaint
        fields = [
            'id', 'complaint_number', 'title', 'complaint_type', 'status', 'priority',
            'complainant_name', 'department_name', 'assigned_to_name', 'is_anonymous',
            'is_urgent', 'days_since_submission', 'is_overdue', 'attachment_count',
            'created_at', 'updated_at'
        ]
    
    def get_attachment_count(self, obj):
        return obj.attachments.count()


class ComplaintDetailSerializer(serializers.ModelSerializer):
    """Serializer for complaint details"""
    complainant_name = serializers.CharField(source='complainant.full_name', read_only=True)
    complainant_email = serializers.CharField(source='complainant.email', read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.full_name', read_only=True)
    resolved_by_name = serializers.CharField(source='resolved_by.full_name', read_only=True)
    days_since_submission = serializers.IntegerField(read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    
    attachments = ComplaintAttachmentSerializer(many=True, read_only=True)
    comments = ComplaintCommentSerializer(many=True, read_only=True)
    status_history = ComplaintStatusHistorySerializer(many=True, read_only=True)
    feedback = ComplaintFeedbackSerializer(read_only=True)
    
    class Meta:
        model = Complaint
        fields = [
            'id', 'complaint_number', 'title', 'description', 'complaint_type',
            'status', 'priority', 'complainant', 'complainant_name', 'complainant_email',
            'department', 'department_name', 'assigned_to', 'assigned_to_name',
            'incident_location', 'incident_date', 'resolution', 'resolved_by',
            'resolved_by_name', 'resolved_at', 'is_anonymous', 'is_urgent',
            'days_since_submission', 'is_overdue', 'created_at', 'updated_at',
            'attachments', 'comments', 'status_history', 'feedback'
        ]


class ComplaintCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating complaints"""
    department_code = serializers.CharField(write_only=True)
    attachments = serializers.ListField(
        child=serializers.FileField(),
        write_only=True,
        required=False
    )
    
    class Meta:
        model = Complaint
        fields = [
            'title', 'description', 'complaint_type', 'department_code',
            'incident_location', 'incident_date', 'is_anonymous', 'is_urgent',
            'attachments'
        ]
    
    def validate_department_code(self, value):
        """Validate department exists"""
        try:
            Department.objects.get(code=value.upper(), is_active=True)
        except Department.DoesNotExist:
            raise serializers.ValidationError("Invalid department code.")
        return value.upper()
    
    def create(self, validated_data):
        """Create complaint with attachments"""
        department_code = validated_data.pop('department_code')
        attachments_data = validated_data.pop('attachments', [])
        
        # Get department
        department = Department.objects.get(code=department_code)
        
        # Create complaint
        complaint = Complaint.objects.create(
            department=department,
            complainant=self.context['request'].user,
            **validated_data
        )
        
        # Create attachments
        for attachment_file in attachments_data:
            ComplaintAttachment.objects.create(
                complaint=complaint,
                file=attachment_file
            )
        
        return complaint


class ComplaintUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating complaints (admin/department officer)"""
    
    class Meta:
        model = Complaint
        fields = [
            'status', 'priority', 'assigned_to', 'resolution', 'resolved_by', 'resolved_at'
        ]
    
    def validate_assigned_to(self, value):
        """Validate assigned user is department officer or admin"""
        if value and value.role not in ['admin', 'department_officer']:
            raise serializers.ValidationError("Can only assign to admin or department officer.")
        return value
    
    def update(self, instance, validated_data):
        """Update complaint and create status history"""
        old_status = instance.status
        new_status = validated_data.get('status', old_status)
        
        # Update complaint
        complaint = super().update(instance, validated_data)
        
        # Create status history if status changed
        if old_status != new_status:
            ComplaintStatusHistory.objects.create(
                complaint=complaint,
                previous_status=old_status,
                new_status=new_status,
                changed_by=self.context['request'].user,
                comment=f"Status changed from {old_status} to {new_status}"
            )
        
        return complaint


class ComplaintCommentCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating complaint comments"""
    
    class Meta:
        model = ComplaintComment
        fields = ['content', 'is_internal']
    
    def create(self, validated_data):
        """Create comment"""
        return ComplaintComment.objects.create(
            complaint=self.context['complaint'],
            author=self.context['request'].user,
            **validated_data
        )


class ComplaintStatsSerializer(serializers.Serializer):
    """Serializer for complaint statistics"""
    total_complaints = serializers.IntegerField()
    pending_complaints = serializers.IntegerField()
    resolved_complaints = serializers.IntegerField()
    overdue_complaints = serializers.IntegerField()
    complaints_by_status = serializers.ListField()
    complaints_by_type = serializers.ListField()
    complaints_by_department = serializers.ListField()
    monthly_trends = serializers.ListField()
    average_resolution_time = serializers.FloatField()
    satisfaction_rating = serializers.FloatField()


class DashboardStatsSerializer(serializers.Serializer):
    """Serializer for dashboard statistics"""
    complaints = ComplaintStatsSerializer()
    users = serializers.DictField()
    departments = serializers.DictField()
    recent_activity = serializers.DictField()
    top_complaint_types = serializers.ListField()
    resolution_trends = serializers.ListField()
