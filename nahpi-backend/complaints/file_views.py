from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.http import HttpResponse, Http404
from django.core.files.storage import default_storage
from django.conf import settings
import os
import mimetypes
from PIL import Image
import uuid

from .models import Complaint, ComplaintAttachment
from .serializers import ComplaintAttachmentSerializer
from accounts.permissions import IsComplaintOwnerOrStaff


class ComplaintAttachmentListView(generics.ListCreateAPIView):
    """List and upload complaint attachments"""
    serializer_class = ComplaintAttachmentSerializer
    permission_classes = [IsComplaintOwnerOrStaff]
    
    def get_queryset(self):
        complaint_id = self.kwargs['complaint_id']
        return ComplaintAttachment.objects.filter(complaint_id=complaint_id)
    
    def create(self, request, *args, **kwargs):
        complaint_id = self.kwargs['complaint_id']
        
        try:
            complaint = Complaint.objects.get(id=complaint_id)
        except Complaint.DoesNotExist:
            return Response({
                'error': 'Complaint not found.'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Check permissions
        self.check_object_permissions(request, complaint)
        
        # Get uploaded files
        files = request.FILES.getlist('files')
        if not files:
            return Response({
                'error': 'No files provided.'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        attachments = []
        errors = []
        
        for file in files:
            try:
                # Validate file
                validation_result = self.validate_file(file)
                if validation_result['valid']:
                    # Create attachment
                    attachment = ComplaintAttachment.objects.create(
                        complaint=complaint,
                        file=file,
                        original_filename=file.name,
                        content_type=file.content_type
                    )
                    attachments.append(attachment)
                else:
                    errors.append({
                        'filename': file.name,
                        'error': validation_result['error']
                    })
            except Exception as e:
                errors.append({
                    'filename': file.name,
                    'error': str(e)
                })
        
        response_data = {
            'message': f'{len(attachments)} files uploaded successfully.',
            'attachments': ComplaintAttachmentSerializer(attachments, many=True).data,
            'uploaded_count': len(attachments),
            'total_files': len(files)
        }
        
        if errors:
            response_data['errors'] = errors
        
        return Response(response_data, status=status.HTTP_201_CREATED)
    
    def validate_file(self, file):
        """Validate uploaded file"""
        # Check file size
        max_size = getattr(settings, 'FILE_UPLOAD_MAX_MEMORY_SIZE', 5 * 1024 * 1024)  # 5MB
        if file.size > max_size:
            return {
                'valid': False,
                'error': f'File size exceeds maximum allowed size of {max_size // (1024*1024)}MB.'
            }
        
        # Check file type
        allowed_extensions = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif', 'txt']
        file_extension = file.name.split('.')[-1].lower()
        if file_extension not in allowed_extensions:
            return {
                'valid': False,
                'error': f'File type .{file_extension} is not allowed. Allowed types: {", ".join(allowed_extensions)}'
            }
        
        # Additional validation for images
        if file_extension in ['jpg', 'jpeg', 'png', 'gif']:
            try:
                # Validate image
                image = Image.open(file)
                image.verify()
                file.seek(0)  # Reset file pointer
            except Exception:
                return {
                    'valid': False,
                    'error': 'Invalid image file.'
                }
        
        return {'valid': True}


class ComplaintAttachmentDetailView(generics.RetrieveDestroyAPIView):
    """Retrieve or delete complaint attachment"""
    serializer_class = ComplaintAttachmentSerializer
    permission_classes = [IsComplaintOwnerOrStaff]
    
    def get_queryset(self):
        complaint_id = self.kwargs['complaint_id']
        return ComplaintAttachment.objects.filter(complaint_id=complaint_id)
    
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        
        # Check permissions on the complaint
        self.check_object_permissions(request, instance.complaint)
        
        # Delete the file from storage
        if instance.file:
            try:
                default_storage.delete(instance.file.name)
            except Exception as e:
                print(f"Error deleting file: {e}")
        
        # Delete the database record
        instance.delete()
        
        return Response({
            'message': 'Attachment deleted successfully.'
        }, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsComplaintOwnerOrStaff])
def download_attachment(request, complaint_id, attachment_id):
    """Download complaint attachment"""
    try:
        attachment = ComplaintAttachment.objects.get(
            id=attachment_id,
            complaint_id=complaint_id
        )
    except ComplaintAttachment.DoesNotExist:
        raise Http404("Attachment not found")
    
    # Check permissions on the complaint
    if not request.user.is_authenticated:
        return Response({
            'error': 'Authentication required.'
        }, status=status.HTTP_401_UNAUTHORIZED)
    
    # Check object permissions
    complaint = attachment.complaint
    if request.user.role == 'student' and complaint.complainant != request.user:
        return Response({
            'error': 'Permission denied.'
        }, status=status.HTTP_403_FORBIDDEN)
    elif request.user.role == 'department_officer' and complaint.department != request.user.department:
        return Response({
            'error': 'Permission denied.'
        }, status=status.HTTP_403_FORBIDDEN)
    
    # Serve the file
    try:
        file_path = attachment.file.path
        if not os.path.exists(file_path):
            raise Http404("File not found on disk")
        
        # Determine content type
        content_type, _ = mimetypes.guess_type(file_path)
        if not content_type:
            content_type = 'application/octet-stream'
        
        # Read file content
        with open(file_path, 'rb') as file:
            response = HttpResponse(file.read(), content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="{attachment.original_filename}"'
            response['Content-Length'] = attachment.file_size
            return response
            
    except Exception as e:
        return Response({
            'error': f'Error serving file: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsComplaintOwnerOrStaff])
def preview_attachment(request, complaint_id, attachment_id):
    """Preview complaint attachment (for images and PDFs)"""
    try:
        attachment = ComplaintAttachment.objects.get(
            id=attachment_id,
            complaint_id=complaint_id
        )
    except ComplaintAttachment.DoesNotExist:
        raise Http404("Attachment not found")
    
    # Check permissions (same as download)
    complaint = attachment.complaint
    if request.user.role == 'student' and complaint.complainant != request.user:
        return Response({
            'error': 'Permission denied.'
        }, status=status.HTTP_403_FORBIDDEN)
    elif request.user.role == 'department_officer' and complaint.department != request.user.department:
        return Response({
            'error': 'Permission denied.'
        }, status=status.HTTP_403_FORBIDDEN)
    
    # Check if file is previewable
    previewable_types = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf']
    if attachment.content_type not in previewable_types:
        return Response({
            'error': 'File type not previewable.'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        file_path = attachment.file.path
        if not os.path.exists(file_path):
            raise Http404("File not found on disk")
        
        # For images, we can serve directly
        if attachment.content_type.startswith('image/'):
            with open(file_path, 'rb') as file:
                response = HttpResponse(file.read(), content_type=attachment.content_type)
                response['Content-Disposition'] = f'inline; filename="{attachment.original_filename}"'
                return response
        
        # For PDFs, serve with inline disposition
        elif attachment.content_type == 'application/pdf':
            with open(file_path, 'rb') as file:
                response = HttpResponse(file.read(), content_type='application/pdf')
                response['Content-Disposition'] = f'inline; filename="{attachment.original_filename}"'
                return response
                
    except Exception as e:
        return Response({
            'error': f'Error serving file: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def file_upload_info(request):
    """Get file upload configuration and limits"""
    max_size = getattr(settings, 'FILE_UPLOAD_MAX_MEMORY_SIZE', 5 * 1024 * 1024)
    allowed_extensions = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif', 'txt']
    
    return Response({
        'max_file_size': max_size,
        'max_file_size_mb': max_size // (1024 * 1024),
        'allowed_extensions': allowed_extensions,
        'allowed_mime_types': [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/png',
            'image/gif',
            'text/plain'
        ]
    }, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def validate_file_upload(request):
    """Validate file before upload"""
    if 'file' not in request.FILES:
        return Response({
            'error': 'No file provided.'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    file = request.FILES['file']
    
    # Create temporary attachment view instance for validation
    view = ComplaintAttachmentListView()
    validation_result = view.validate_file(file)
    
    if validation_result['valid']:
        return Response({
            'valid': True,
            'message': 'File is valid for upload.',
            'file_info': {
                'name': file.name,
                'size': file.size,
                'content_type': file.content_type
            }
        }, status=status.HTTP_200_OK)
    else:
        return Response({
            'valid': False,
            'error': validation_result['error']
        }, status=status.HTTP_400_BAD_REQUEST)


class FileManagementStatsView(APIView):
    """Get file management statistics"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        user = request.user
        
        if user.role == 'admin':
            # Admin can see all stats
            total_files = ComplaintAttachment.objects.count()
            total_size = sum(
                attachment.file_size for attachment in ComplaintAttachment.objects.all()
            )
            files_by_type = {}
            for attachment in ComplaintAttachment.objects.all():
                ext = attachment.original_filename.split('.')[-1].lower()
                files_by_type[ext] = files_by_type.get(ext, 0) + 1
                
        elif user.role == 'department_officer':
            # Department officer can see department stats
            attachments = ComplaintAttachment.objects.filter(
                complaint__department=user.department
            )
            total_files = attachments.count()
            total_size = sum(attachment.file_size for attachment in attachments)
            files_by_type = {}
            for attachment in attachments:
                ext = attachment.original_filename.split('.')[-1].lower()
                files_by_type[ext] = files_by_type.get(ext, 0) + 1
                
        else:
            # Student can see their own stats
            attachments = ComplaintAttachment.objects.filter(
                complaint__complainant=user
            )
            total_files = attachments.count()
            total_size = sum(attachment.file_size for attachment in attachments)
            files_by_type = {}
            for attachment in attachments:
                ext = attachment.original_filename.split('.')[-1].lower()
                files_by_type[ext] = files_by_type.get(ext, 0) + 1
        
        return Response({
            'total_files': total_files,
            'total_size_bytes': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'files_by_type': files_by_type,
            'upload_limits': {
                'max_file_size_mb': getattr(settings, 'FILE_UPLOAD_MAX_MEMORY_SIZE', 5 * 1024 * 1024) // (1024 * 1024),
                'allowed_extensions': ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif', 'txt']
            }
        }, status=status.HTTP_200_OK)
