from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from complaints.file_utils import cleanup_old_files, get_storage_usage


class Command(BaseCommand):
    help = 'Clean up old files and manage storage'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='Number of days old files should be before cleanup (default: 30)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting'
        )
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Show storage usage statistics'
        )

    def handle(self, *args, **options):
        if options['stats']:
            self.show_storage_stats()
            return

        days_old = options['days']
        dry_run = options['dry_run']

        self.stdout.write(f'Starting file cleanup for files older than {days_old} days...')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No files will be deleted'))
        
        try:
            if not dry_run:
                deleted_count = cleanup_old_files(days_old)
                self.stdout.write(
                    self.style.SUCCESS(f'Successfully cleaned up {deleted_count} old files')
                )
            else:
                # For dry run, just count what would be deleted
                from complaints.models import ComplaintAttachment
                cutoff_date = timezone.now() - timedelta(days=days_old)
                
                old_attachments = ComplaintAttachment.objects.filter(
                    complaint__status__in=['resolved', 'closed'],
                    complaint__resolved_at__lt=cutoff_date
                )
                
                count = old_attachments.count()
                self.stdout.write(f'Would delete {count} files in actual run')
                
                # Show some examples
                for attachment in old_attachments[:5]:
                    self.stdout.write(f'  - {attachment.original_filename} ({attachment.file_size} bytes)')
                
                if count > 5:
                    self.stdout.write(f'  ... and {count - 5} more files')
        
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error during cleanup: {str(e)}')
            )

    def show_storage_stats(self):
        """Show storage usage statistics"""
        self.stdout.write('Storage Usage Statistics:')
        self.stdout.write('=' * 50)
        
        try:
            stats = get_storage_usage()
            
            self.stdout.write(f'Total Files: {stats["total_files"]}')
            self.stdout.write(f'Total Size: {stats["total_size_mb"]} MB ({stats["total_size_bytes"]} bytes)')
            self.stdout.write('')
            
            self.stdout.write('Usage by File Type:')
            self.stdout.write('-' * 30)
            
            for file_type, usage in stats['usage_by_type'].items():
                size_mb = round(usage['size'] / (1024 * 1024), 2)
                self.stdout.write(f'{file_type.upper()}: {usage["count"]} files, {size_mb} MB')
        
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error getting storage stats: {str(e)}')
            )
