# Generated by Django 5.2.4 on 2025-07-06 10:37

import complaints.models
import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Complaint',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('complaint_number', models.CharField(editable=False, max_length=20, unique=True)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('complaint_type', models.CharField(choices=[('academic', 'Academic'), ('administrative', 'Administrative'), ('facility', 'Facility'), ('harassment', 'Harassment'), ('discrimination', 'Discrimination'), ('financial', 'Financial'), ('health', 'Health & Safety'), ('other', 'Other')], max_length=20)),
                ('status', models.CharField(choices=[('submitted', 'Submitted'), ('under_review', 'Under Review'), ('in_progress', 'In Progress'), ('resolved', 'Resolved'), ('closed', 'Closed'), ('rejected', 'Rejected')], default='submitted', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10)),
                ('incident_location', models.CharField(blank=True, max_length=200)),
                ('incident_date', models.DateTimeField(blank=True, null=True)),
                ('resolution', models.TextField(blank=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_anonymous', models.BooleanField(default=False)),
                ('is_urgent', models.BooleanField(default=False)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_complaints', to=settings.AUTH_USER_MODEL)),
                ('complainant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='complaints', to=settings.AUTH_USER_MODEL)),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='complaints', to='accounts.department')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_complaints', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Complaint',
                'verbose_name_plural': 'Complaints',
                'db_table': 'complaints_complaint',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ComplaintAttachment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('file', models.FileField(upload_to=complaints.models.complaint_attachment_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'])])),
                ('original_filename', models.CharField(max_length=255)),
                ('file_size', models.PositiveIntegerField()),
                ('content_type', models.CharField(max_length=100)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('complaint', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='complaints.complaint')),
            ],
            options={
                'verbose_name': 'Complaint Attachment',
                'verbose_name_plural': 'Complaint Attachments',
                'db_table': 'complaints_attachment',
            },
        ),
        migrations.CreateModel(
            name='ComplaintComment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('content', models.TextField()),
                ('is_internal', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('complaint', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='complaints.complaint')),
            ],
            options={
                'verbose_name': 'Complaint Comment',
                'verbose_name_plural': 'Complaint Comments',
                'db_table': 'complaints_comment',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='ComplaintFeedback',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('satisfaction_rating', models.IntegerField(choices=[(1, 'Very Dissatisfied'), (2, 'Dissatisfied'), (3, 'Neutral'), (4, 'Satisfied'), (5, 'Very Satisfied')])),
                ('feedback_text', models.TextField(blank=True)),
                ('response_time_rating', models.IntegerField(blank=True, choices=[(1, 'Very Dissatisfied'), (2, 'Dissatisfied'), (3, 'Neutral'), (4, 'Satisfied'), (5, 'Very Satisfied')], null=True)),
                ('resolution_quality_rating', models.IntegerField(blank=True, choices=[(1, 'Very Dissatisfied'), (2, 'Dissatisfied'), (3, 'Neutral'), (4, 'Satisfied'), (5, 'Very Satisfied')], null=True)),
                ('staff_helpfulness_rating', models.IntegerField(blank=True, choices=[(1, 'Very Dissatisfied'), (2, 'Dissatisfied'), (3, 'Neutral'), (4, 'Satisfied'), (5, 'Very Satisfied')], null=True)),
                ('suggestions', models.TextField(blank=True)),
                ('would_recommend', models.BooleanField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('complaint', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='feedback', to='complaints.complaint')),
            ],
            options={
                'verbose_name': 'Complaint Feedback',
                'verbose_name_plural': 'Complaint Feedback',
                'db_table': 'complaints_feedback',
            },
        ),
        migrations.CreateModel(
            name='ComplaintStatusHistory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('previous_status', models.CharField(choices=[('submitted', 'Submitted'), ('under_review', 'Under Review'), ('in_progress', 'In Progress'), ('resolved', 'Resolved'), ('closed', 'Closed'), ('rejected', 'Rejected')], max_length=20)),
                ('new_status', models.CharField(choices=[('submitted', 'Submitted'), ('under_review', 'Under Review'), ('in_progress', 'In Progress'), ('resolved', 'Resolved'), ('closed', 'Closed'), ('rejected', 'Rejected')], max_length=20)),
                ('comment', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('changed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('complaint', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_history', to='complaints.complaint')),
            ],
            options={
                'verbose_name': 'Status History',
                'verbose_name_plural': 'Status Histories',
                'db_table': 'complaints_statushistory',
                'ordering': ['-timestamp'],
            },
        ),
    ]
