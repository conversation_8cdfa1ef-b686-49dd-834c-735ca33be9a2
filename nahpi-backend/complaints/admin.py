from django.contrib import admin
from .models import (
    Complain<PERSON>, ComplaintAttachment, ComplaintStatusHistory,
    ComplaintFeedback, ComplaintComment
)


@admin.register(Complaint)
class ComplaintAdmin(admin.ModelAdmin):
    """Admin configuration for Complaint model"""

    list_display = [
        'complaint_number', 'title', 'complainant', 'department',
        'status', 'priority', 'is_urgent', 'created_at'
    ]
    list_filter = [
        'status', 'priority', 'complaint_type', 'department',
        'is_urgent', 'is_anonymous', 'created_at'
    ]
    search_fields = [
        'complaint_number', 'title', 'description',
        'complainant__email', 'complainant__first_name', 'complainant__last_name'
    ]
    ordering = ['-created_at']
    readonly_fields = ['complaint_number', 'created_at', 'updated_at', 'days_since_submission']

    fieldsets = (
        ('Basic Information', {
            'fields': ('complaint_number', 'title', 'description', 'complaint_type')
        }),
        ('Assignment', {
            'fields': ('complainant', 'department', 'assigned_to')
        }),
        ('Status & Priority', {
            'fields': ('status', 'priority', 'is_urgent', 'is_anonymous')
        }),
        ('Incident Details', {
            'fields': ('incident_location', 'incident_date')
        }),
        ('Resolution', {
            'fields': ('resolution', 'resolved_by', 'resolved_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'days_since_submission')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'complainant', 'department', 'assigned_to', 'resolved_by'
        )


@admin.register(ComplaintAttachment)
class ComplaintAttachmentAdmin(admin.ModelAdmin):
    """Admin configuration for ComplaintAttachment model"""

    list_display = ['complaint', 'original_filename', 'file_size', 'uploaded_at']
    list_filter = ['content_type', 'uploaded_at']
    search_fields = ['complaint__complaint_number', 'original_filename']
    readonly_fields = ['file_size', 'content_type', 'uploaded_at']


@admin.register(ComplaintStatusHistory)
class ComplaintStatusHistoryAdmin(admin.ModelAdmin):
    """Admin configuration for ComplaintStatusHistory model"""

    list_display = ['complaint', 'previous_status', 'new_status', 'changed_by', 'timestamp']
    list_filter = ['previous_status', 'new_status', 'timestamp']
    search_fields = ['complaint__complaint_number', 'changed_by__email', 'comment']
    readonly_fields = ['timestamp']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('complaint', 'changed_by')


@admin.register(ComplaintFeedback)
class ComplaintFeedbackAdmin(admin.ModelAdmin):
    """Admin configuration for ComplaintFeedback model"""

    list_display = [
        'complaint', 'satisfaction_rating', 'response_time_rating',
        'resolution_quality_rating', 'would_recommend', 'created_at'
    ]
    list_filter = [
        'satisfaction_rating', 'response_time_rating', 'resolution_quality_rating',
        'would_recommend', 'created_at'
    ]
    search_fields = ['complaint__complaint_number', 'feedback_text', 'suggestions']
    readonly_fields = ['average_rating', 'created_at', 'updated_at']


@admin.register(ComplaintComment)
class ComplaintCommentAdmin(admin.ModelAdmin):
    """Admin configuration for ComplaintComment model"""

    list_display = ['complaint', 'author', 'is_internal', 'created_at']
    list_filter = ['is_internal', 'created_at']
    search_fields = ['complaint__complaint_number', 'author__email', 'content']
    readonly_fields = ['created_at', 'updated_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('complaint', 'author')
