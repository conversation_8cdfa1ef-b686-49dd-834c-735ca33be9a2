from django.db import models
from django.conf import settings
from django.core.validators import FileExtensionValidator
import uuid
import os

def complaint_attachment_path(instance, filename):
    """Generate file path for complaint attachments"""
    return f'complaints/{instance.complaint.id}/attachments/{filename}'

class Complaint(models.Model):
    """
    Main complaint model
    """

    COMPLAINT_TYPES = [
        ('academic', 'Academic'),
        ('administrative', 'Administrative'),
        ('facility', 'Facility'),
        ('harassment', 'Harassment'),
        ('discrimination', 'Discrimination'),
        ('financial', 'Financial'),
        ('health', 'Health & Safety'),
        ('other', 'Other'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    STATUS_CHOICES = [
        ('submitted', 'Submitted'),
        ('under_review', 'Under Review'),
        ('in_progress', 'In Progress'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
        ('rejected', 'Rejected'),
    ]

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    complaint_number = models.CharField(max_length=20, unique=True, editable=False)

    # Complainant Information
    complainant = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='complaints'
    )

    # Complaint Details
    title = models.CharField(max_length=200)
    description = models.TextField()
    complaint_type = models.CharField(max_length=20, choices=COMPLAINT_TYPES)
    department = models.ForeignKey(
        'accounts.Department',
        on_delete=models.CASCADE,
        related_name='complaints'
    )

    # Status and Priority
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='submitted')
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='medium')

    # Assignment (Optional - complaints go directly to departments)
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_complaints'
    )

    # Location and Context
    incident_location = models.CharField(max_length=200, blank=True)
    incident_date = models.DateTimeField(null=True, blank=True)

    # Resolution
    resolution = models.TextField(blank=True)
    resolved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_complaints'
    )
    resolved_at = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Tracking
    is_anonymous = models.BooleanField(default=False)
    is_urgent = models.BooleanField(default=False)

    class Meta:
        db_table = 'complaints_complaint'
        verbose_name = 'Complaint'
        verbose_name_plural = 'Complaints'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.complaint_number} - {self.title}"

    def save(self, *args, **kwargs):
        if not self.complaint_number:
            # Generate complaint number: NAHPI-YYYY-NNNN
            from datetime import datetime
            year = datetime.now().year
            count = Complaint.objects.filter(created_at__year=year).count() + 1
            self.complaint_number = f"NAHPI-{year}-{count:04d}"
        super().save(*args, **kwargs)

    @property
    def days_since_submission(self):
        from django.utils import timezone
        return (timezone.now() - self.created_at).days

    @property
    def is_overdue(self):
        """Check if complaint is overdue (more than 7 days without resolution)"""
        return self.days_since_submission > 7 and self.status not in ['resolved', 'closed']


class ComplaintAttachment(models.Model):
    """
    File attachments for complaints
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    complaint = models.ForeignKey(
        Complaint,
        on_delete=models.CASCADE,
        related_name='attachments'
    )

    file = models.FileField(
        upload_to=complaint_attachment_path,
        validators=[
            FileExtensionValidator(
                allowed_extensions=['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif']
            )
        ]
    )
    original_filename = models.CharField(max_length=255)
    file_size = models.PositiveIntegerField()  # in bytes
    content_type = models.CharField(max_length=100)

    # Timestamps
    uploaded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'complaints_attachment'
        verbose_name = 'Complaint Attachment'
        verbose_name_plural = 'Complaint Attachments'

    def __str__(self):
        return f"Attachment for {self.complaint.complaint_number}"

    def save(self, *args, **kwargs):
        if self.file:
            self.file_size = self.file.size
            self.original_filename = self.file.name
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        # Delete the actual file when the model instance is deleted
        if self.file:
            if os.path.isfile(self.file.path):
                os.remove(self.file.path)
        super().delete(*args, **kwargs)


class ComplaintStatusHistory(models.Model):
    """
    Track status changes for complaints
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    complaint = models.ForeignKey(
        Complaint,
        on_delete=models.CASCADE,
        related_name='status_history'
    )

    previous_status = models.CharField(max_length=20, choices=Complaint.STATUS_CHOICES)
    new_status = models.CharField(max_length=20, choices=Complaint.STATUS_CHOICES)

    changed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True
    )

    comment = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'complaints_statushistory'
        verbose_name = 'Status History'
        verbose_name_plural = 'Status Histories'
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.complaint.complaint_number}: {self.previous_status} → {self.new_status}"


class ComplaintFeedback(models.Model):
    """
    Feedback from complainants on resolved complaints
    """
    SATISFACTION_LEVELS = [
        (1, 'Very Dissatisfied'),
        (2, 'Dissatisfied'),
        (3, 'Neutral'),
        (4, 'Satisfied'),
        (5, 'Very Satisfied'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    complaint = models.OneToOneField(
        Complaint,
        on_delete=models.CASCADE,
        related_name='feedback'
    )

    satisfaction_rating = models.IntegerField(choices=SATISFACTION_LEVELS)
    feedback_text = models.TextField(blank=True)

    # Specific ratings
    response_time_rating = models.IntegerField(choices=SATISFACTION_LEVELS, null=True, blank=True)
    resolution_quality_rating = models.IntegerField(choices=SATISFACTION_LEVELS, null=True, blank=True)
    staff_helpfulness_rating = models.IntegerField(choices=SATISFACTION_LEVELS, null=True, blank=True)

    # Additional feedback
    suggestions = models.TextField(blank=True)
    would_recommend = models.BooleanField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'complaints_feedback'
        verbose_name = 'Complaint Feedback'
        verbose_name_plural = 'Complaint Feedback'

    def __str__(self):
        return f"Feedback for {self.complaint.complaint_number}"

    @property
    def average_rating(self):
        """Calculate average rating across all rating fields"""
        ratings = [
            self.satisfaction_rating,
            self.response_time_rating,
            self.resolution_quality_rating,
            self.staff_helpfulness_rating
        ]
        valid_ratings = [r for r in ratings if r is not None]
        return sum(valid_ratings) / len(valid_ratings) if valid_ratings else 0


class ComplaintComment(models.Model):
    """
    Comments and updates on complaints
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    complaint = models.ForeignKey(
        Complaint,
        on_delete=models.CASCADE,
        related_name='comments'
    )

    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE
    )

    content = models.TextField()
    is_internal = models.BooleanField(default=False)  # Internal comments not visible to complainant

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'complaints_comment'
        verbose_name = 'Complaint Comment'
        verbose_name_plural = 'Complaint Comments'
        ordering = ['created_at']

    def __str__(self):
        return f"Comment on {self.complaint.complaint_number} by {self.author.email}"
