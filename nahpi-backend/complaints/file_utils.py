import os
import uuid
import hashlib
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from PIL import Image
import magic
from datetime import datetime, timed<PERSON><PERSON>


def generate_secure_filename(original_filename):
    """Generate a secure filename with UUID"""
    file_extension = original_filename.split('.')[-1].lower()
    secure_name = f"{uuid.uuid4().hex}.{file_extension}"
    return secure_name


def get_file_hash(file):
    """Generate MD5 hash of file content"""
    hasher = hashlib.md5()
    for chunk in file.chunks():
        hasher.update(chunk)
    return hasher.hexdigest()


def validate_file_type(file):
    """Validate file type using python-magic"""
    try:
        # Read first 1024 bytes to determine file type
        file_start = file.read(1024)
        file.seek(0)  # Reset file pointer
        
        # Use python-magic to detect actual file type
        file_type = magic.from_buffer(file_start, mime=True)
        
        # Define allowed MIME types
        allowed_types = {
            'application/pdf': ['pdf'],
            'application/msword': ['doc'],
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['docx'],
            'image/jpeg': ['jpg', 'jpeg'],
            'image/png': ['png'],
            'image/gif': ['gif'],
            'text/plain': ['txt'],
        }
        
        # Check if detected type is allowed
        if file_type in allowed_types:
            # Verify file extension matches detected type
            file_extension = file.name.split('.')[-1].lower()
            if file_extension in allowed_types[file_type]:
                return {'valid': True, 'detected_type': file_type}
            else:
                return {
                    'valid': False,
                    'error': f'File extension .{file_extension} does not match detected file type {file_type}'
                }
        else:
            return {
                'valid': False,
                'error': f'File type {file_type} is not allowed'
            }
            
    except Exception as e:
        return {
            'valid': False,
            'error': f'Error validating file type: {str(e)}'
        }


def scan_file_for_malware(file):
    """Placeholder for malware scanning"""
    # In production, integrate with antivirus service like ClamAV
    # For now, just check file size and basic patterns
    
    try:
        # Check for suspicious file patterns
        file_content = file.read(1024)  # Read first 1KB
        file.seek(0)  # Reset file pointer
        
        # Basic checks for suspicious content
        suspicious_patterns = [
            b'<script',
            b'javascript:',
            b'vbscript:',
            b'onload=',
            b'onerror=',
        ]
        
        content_lower = file_content.lower()
        for pattern in suspicious_patterns:
            if pattern in content_lower:
                return {
                    'safe': False,
                    'reason': 'Suspicious content detected'
                }
        
        return {'safe': True}
        
    except Exception as e:
        return {
            'safe': False,
            'reason': f'Error scanning file: {str(e)}'
        }


def compress_image(image_file, max_size=(1920, 1080), quality=85):
    """Compress image file if it's too large"""
    try:
        # Open image
        image = Image.open(image_file)
        
        # Convert RGBA to RGB if necessary
        if image.mode in ('RGBA', 'LA', 'P'):
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
            image = background
        
        # Resize if image is larger than max_size
        if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
            image.thumbnail(max_size, Image.Resampling.LANCZOS)
        
        # Save compressed image
        from io import BytesIO
        output = BytesIO()
        image.save(output, format='JPEG', quality=quality, optimize=True)
        output.seek(0)
        
        return ContentFile(output.read(), name=image_file.name)
        
    except Exception as e:
        # If compression fails, return original file
        print(f"Image compression failed: {e}")
        return image_file


def create_thumbnail(image_file, size=(150, 150)):
    """Create thumbnail for image file"""
    try:
        image = Image.open(image_file)
        
        # Convert to RGB if necessary
        if image.mode in ('RGBA', 'LA', 'P'):
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
            image = background
        
        # Create thumbnail
        image.thumbnail(size, Image.Resampling.LANCZOS)
        
        # Save thumbnail
        from io import BytesIO
        output = BytesIO()
        image.save(output, format='JPEG', quality=90)
        output.seek(0)
        
        return ContentFile(output.read())
        
    except Exception as e:
        print(f"Thumbnail creation failed: {e}")
        return None


def cleanup_old_files(days_old=30):
    """Clean up old temporary and orphaned files"""
    from .models import ComplaintAttachment
    
    cutoff_date = datetime.now() - timedelta(days=days_old)
    
    # Find attachments older than cutoff date for resolved complaints
    old_attachments = ComplaintAttachment.objects.filter(
        complaint__status__in=['resolved', 'closed'],
        complaint__resolved_at__lt=cutoff_date
    )
    
    deleted_count = 0
    for attachment in old_attachments:
        try:
            # Delete file from storage
            if attachment.file:
                default_storage.delete(attachment.file.name)
            
            # Delete database record
            attachment.delete()
            deleted_count += 1
            
        except Exception as e:
            print(f"Error deleting attachment {attachment.id}: {e}")
    
    return deleted_count


def get_storage_usage():
    """Get storage usage statistics"""
    from .models import ComplaintAttachment
    
    total_files = ComplaintAttachment.objects.count()
    total_size = 0
    
    for attachment in ComplaintAttachment.objects.all():
        total_size += attachment.file_size
    
    # Get storage usage by file type
    usage_by_type = {}
    for attachment in ComplaintAttachment.objects.all():
        file_extension = attachment.original_filename.split('.')[-1].lower()
        if file_extension not in usage_by_type:
            usage_by_type[file_extension] = {'count': 0, 'size': 0}
        
        usage_by_type[file_extension]['count'] += 1
        usage_by_type[file_extension]['size'] += attachment.file_size
    
    return {
        'total_files': total_files,
        'total_size_bytes': total_size,
        'total_size_mb': round(total_size / (1024 * 1024), 2),
        'usage_by_type': usage_by_type
    }


def backup_files_to_archive():
    """Backup files to archive storage (placeholder)"""
    # In production, implement backup to cloud storage like AWS S3, Google Cloud Storage
    from .models import ComplaintAttachment
    
    # Find files older than 1 year for resolved complaints
    one_year_ago = datetime.now() - timedelta(days=365)
    old_attachments = ComplaintAttachment.objects.filter(
        complaint__status__in=['resolved', 'closed'],
        complaint__resolved_at__lt=one_year_ago
    )
    
    archived_count = 0
    for attachment in old_attachments:
        try:
            # In production, upload to archive storage
            print(f"Would archive: {attachment.file.name}")
            archived_count += 1
        except Exception as e:
            print(f"Error archiving attachment {attachment.id}: {e}")
    
    return archived_count


def validate_file_security(file):
    """Comprehensive file security validation"""
    validations = []
    
    # 1. File type validation
    type_result = validate_file_type(file)
    validations.append(('file_type', type_result))
    
    # 2. Malware scan
    malware_result = scan_file_for_malware(file)
    validations.append(('malware_scan', malware_result))
    
    # 3. File size check
    max_size = getattr(settings, 'FILE_UPLOAD_MAX_MEMORY_SIZE', 5 * 1024 * 1024)
    size_result = {
        'valid': file.size <= max_size,
        'error': f'File size {file.size} exceeds maximum {max_size}' if file.size > max_size else None
    }
    validations.append(('file_size', size_result))
    
    # 4. Filename validation
    filename_result = validate_filename(file.name)
    validations.append(('filename', filename_result))
    
    # Check if all validations passed
    all_valid = all(result.get('valid', result.get('safe', False)) for _, result in validations)
    
    return {
        'valid': all_valid,
        'validations': validations,
        'errors': [result.get('error', result.get('reason')) for _, result in validations 
                  if not result.get('valid', result.get('safe', False))]
    }


def validate_filename(filename):
    """Validate filename for security"""
    # Check for dangerous characters
    dangerous_chars = ['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|']
    
    for char in dangerous_chars:
        if char in filename:
            return {
                'valid': False,
                'error': f'Filename contains dangerous character: {char}'
            }
    
    # Check filename length
    if len(filename) > 255:
        return {
            'valid': False,
            'error': 'Filename too long (max 255 characters)'
        }
    
    # Check for empty filename
    if not filename.strip():
        return {
            'valid': False,
            'error': 'Filename cannot be empty'
        }
    
    return {'valid': True}


def get_file_info(file):
    """Get comprehensive file information"""
    return {
        'name': file.name,
        'size': file.size,
        'content_type': file.content_type,
        'hash': get_file_hash(file),
        'extension': file.name.split('.')[-1].lower() if '.' in file.name else '',
        'is_image': file.content_type.startswith('image/') if file.content_type else False,
        'is_document': file.content_type in [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ] if file.content_type else False
    }
