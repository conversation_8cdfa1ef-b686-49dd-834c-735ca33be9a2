from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Q, Count, Avg
from django.utils import timezone
from datetime import datetime, timedelta

from .models import Complaint, ComplaintAttachment, ComplaintComment, ComplaintFeedback
from .serializers import (
    ComplaintListSerializer, ComplaintDetailSerializer, ComplaintCreateSerializer,
    ComplaintUpdateSerializer, ComplaintCommentCreateSerializer, ComplaintCommentSerializer,
    ComplaintFeedbackSerializer, ComplaintStatsSerializer, DashboardStatsSerializer
)
from accounts.permissions import (
    IsStudentAndVerified, IsAdminOrDepartmentOfficer, IsComplaintOwnerOrStaff,
    IsDepartmentOfficerForComplaint, CanViewReports
)
from accounts.models import User, Department


class ComplaintListCreateView(generics.ListCreateAPIView):
    """List and create complaints"""
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ComplaintCreateSerializer
        return ComplaintListSerializer

    def get_queryset(self):
        user = self.request.user
        queryset = Complaint.objects.select_related(
            'complainant', 'department', 'assigned_to'
        ).prefetch_related('attachments')

        # Filter based on user role
        if user.role == 'student':
            queryset = queryset.filter(complainant=user)
        elif user.role == 'department_officer':
            queryset = queryset.filter(department=user.department)
        # Admin can see all complaints

        # Apply filters
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        type_filter = self.request.query_params.get('type')
        if type_filter:
            queryset = queryset.filter(complaint_type=type_filter)

        department_filter = self.request.query_params.get('department')
        if department_filter:
            queryset = queryset.filter(department__code=department_filter)

        priority_filter = self.request.query_params.get('priority')
        if priority_filter:
            queryset = queryset.filter(priority=priority_filter)

        # Search
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(description__icontains=search) |
                Q(complaint_number__icontains=search)
            )

        return queryset.order_by('-created_at')

    def create(self, request, *args, **kwargs):
        # Check if user is verified student
        if request.user.role == 'student' and not request.user.is_verified:
            return Response({
                'error': 'You must verify your account before submitting complaints.'
            }, status=status.HTTP_403_FORBIDDEN)

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        complaint = serializer.save()

        return Response({
            'message': 'Complaint submitted successfully.',
            'complaint': ComplaintDetailSerializer(complaint).data
        }, status=status.HTTP_201_CREATED)


class ComplaintDetailView(generics.RetrieveUpdateAPIView):
    """Retrieve and update complaint details"""
    queryset = Complaint.objects.select_related(
        'complainant', 'department', 'assigned_to', 'resolved_by'
    ).prefetch_related(
        'attachments', 'comments__author', 'status_history__changed_by', 'feedback'
    )
    permission_classes = [IsComplaintOwnerOrStaff]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return ComplaintUpdateSerializer
        return ComplaintDetailSerializer

    def update(self, request, *args, **kwargs):
        # Only admin and department officers can update
        if request.user.role not in ['admin', 'department_officer']:
            return Response({
                'error': 'You do not have permission to update complaints.'
            }, status=status.HTTP_403_FORBIDDEN)

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        complaint = serializer.save()

        return Response({
            'message': 'Complaint updated successfully.',
            'complaint': ComplaintDetailSerializer(complaint).data
        }, status=status.HTTP_200_OK)


class ComplaintCommentListCreateView(generics.ListCreateAPIView):
    """List and create complaint comments"""
    serializer_class = ComplaintCommentSerializer
    permission_classes = [IsComplaintOwnerOrStaff]

    def get_queryset(self):
        complaint_id = self.kwargs['complaint_id']
        user = self.request.user

        queryset = ComplaintComment.objects.filter(
            complaint_id=complaint_id
        ).select_related('author')

        # Students can't see internal comments
        if user.role == 'student':
            queryset = queryset.filter(is_internal=False)

        return queryset.order_by('created_at')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ComplaintCommentCreateSerializer
        return ComplaintCommentSerializer

    def create(self, request, *args, **kwargs):
        try:
            complaint = Complaint.objects.get(id=self.kwargs['complaint_id'])
        except Complaint.DoesNotExist:
            return Response({
                'error': 'Complaint not found.'
            }, status=status.HTTP_404_NOT_FOUND)

        # Check permissions
        if not self.check_object_permissions(request, complaint):
            return Response({
                'error': 'You do not have permission to comment on this complaint.'
            }, status=status.HTTP_403_FORBIDDEN)

        serializer = self.get_serializer(
            data=request.data,
            context={'complaint': complaint, 'request': request}
        )
        serializer.is_valid(raise_exception=True)
        comment = serializer.save()

        return Response({
            'message': 'Comment added successfully.',
            'comment': ComplaintCommentSerializer(comment).data
        }, status=status.HTTP_201_CREATED)


class ComplaintFeedbackView(generics.CreateAPIView):
    """Create complaint feedback"""
    serializer_class = ComplaintFeedbackSerializer
    permission_classes = [permissions.IsAuthenticated]

    def create(self, request, *args, **kwargs):
        try:
            complaint = Complaint.objects.get(id=self.kwargs['complaint_id'])
        except Complaint.DoesNotExist:
            return Response({
                'error': 'Complaint not found.'
            }, status=status.HTTP_404_NOT_FOUND)

        # Only complainant can provide feedback
        if complaint.complainant != request.user:
            return Response({
                'error': 'You can only provide feedback for your own complaints.'
            }, status=status.HTTP_403_FORBIDDEN)

        # Only resolved complaints can have feedback
        if complaint.status not in ['resolved', 'closed']:
            return Response({
                'error': 'Feedback can only be provided for resolved complaints.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if feedback already exists
        if hasattr(complaint, 'feedback'):
            return Response({
                'error': 'Feedback has already been provided for this complaint.'
            }, status=status.HTTP_400_BAD_REQUEST)

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        feedback = serializer.save(complaint=complaint)

        return Response({
            'message': 'Feedback submitted successfully.',
            'feedback': ComplaintFeedbackSerializer(feedback).data
        }, status=status.HTTP_201_CREATED)


@api_view(['POST'])
@permission_classes([IsAdminOrDepartmentOfficer])
def assign_complaint(request, complaint_id):
    """Assign complaint to a user"""
    try:
        complaint = Complaint.objects.get(id=complaint_id)
    except Complaint.DoesNotExist:
        return Response({
            'error': 'Complaint not found.'
        }, status=status.HTTP_404_NOT_FOUND)

    # Department officers can only assign complaints in their department
    if request.user.role == 'department_officer':
        if complaint.department != request.user.department:
            return Response({
                'error': 'You can only assign complaints in your department.'
            }, status=status.HTTP_403_FORBIDDEN)

    assignee_id = request.data.get('assignee_id')
    if not assignee_id:
        return Response({
            'error': 'Assignee ID is required.'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        assignee = User.objects.get(id=assignee_id)
    except User.DoesNotExist:
        return Response({
            'error': 'Assignee not found.'
        }, status=status.HTTP_404_NOT_FOUND)

    # Validate assignee role
    if assignee.role not in ['admin', 'department_officer']:
        return Response({
            'error': 'Can only assign to admin or department officer.'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Update complaint
    old_assignee = complaint.assigned_to
    complaint.assigned_to = assignee
    complaint.save()

    # Create status history
    from .models import ComplaintStatusHistory
    ComplaintStatusHistory.objects.create(
        complaint=complaint,
        previous_status=complaint.status,
        new_status=complaint.status,
        changed_by=request.user,
        comment=f"Assigned to {assignee.full_name}"
    )

    return Response({
        'message': f'Complaint assigned to {assignee.full_name} successfully.',
        'complaint': ComplaintDetailSerializer(complaint).data
    }, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([IsAdminOrDepartmentOfficer])
def update_complaint_status(request, complaint_id):
    """Update complaint status"""
    try:
        complaint = Complaint.objects.get(id=complaint_id)
    except Complaint.DoesNotExist:
        return Response({
            'error': 'Complaint not found.'
        }, status=status.HTTP_404_NOT_FOUND)

    # Department officers can only update complaints in their department
    if request.user.role == 'department_officer':
        if complaint.department != request.user.department:
            return Response({
                'error': 'You can only update complaints in your department.'
            }, status=status.HTTP_403_FORBIDDEN)

    new_status = request.data.get('status')
    comment = request.data.get('comment', '')

    if not new_status:
        return Response({
            'error': 'Status is required.'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Validate status
    valid_statuses = [choice[0] for choice in Complaint.STATUS_CHOICES]
    if new_status not in valid_statuses:
        return Response({
            'error': 'Invalid status.'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Update complaint
    old_status = complaint.status
    complaint.status = new_status

    # Set resolved fields if status is resolved
    if new_status in ['resolved', 'closed']:
        complaint.resolved_by = request.user
        complaint.resolved_at = timezone.now()
        if request.data.get('resolution'):
            complaint.resolution = request.data.get('resolution')

    complaint.save()

    # Create status history
    from .models import ComplaintStatusHistory
    ComplaintStatusHistory.objects.create(
        complaint=complaint,
        previous_status=old_status,
        new_status=new_status,
        changed_by=request.user,
        comment=comment or f"Status changed from {old_status} to {new_status}"
    )

    return Response({
        'message': 'Complaint status updated successfully.',
        'complaint': ComplaintDetailSerializer(complaint).data
    }, status=status.HTTP_200_OK)


# ============================================================================
# ANALYTICS AND DASHBOARD VIEWS
# ============================================================================

@api_view(['GET'])
@permission_classes([CanViewReports])
def complaint_analytics(request):
    """Get complaint analytics and statistics"""
    user = request.user

    # Base queryset based on user role
    if user.role == 'admin':
        queryset = Complaint.objects.all()
    elif user.role == 'department_officer':
        queryset = Complaint.objects.filter(department=user.department)
    else:
        return Response({
            'error': 'Permission denied.'
        }, status=status.HTTP_403_FORBIDDEN)

    # Basic counts
    total_complaints = queryset.count()
    pending_complaints = queryset.filter(status__in=['submitted', 'under_review', 'in_progress']).count()
    resolved_complaints = queryset.filter(status__in=['resolved', 'closed']).count()
    overdue_complaints = queryset.filter(
        status__in=['submitted', 'under_review', 'in_progress'],
        created_at__lt=timezone.now() - timedelta(days=7)
    ).count()

    # Complaints by status
    complaints_by_status = list(
        queryset.values('status').annotate(count=Count('id')).order_by('-count')
    )

    # Complaints by type
    complaints_by_type = list(
        queryset.values('complaint_type').annotate(count=Count('id')).order_by('-count')
    )

    # Complaints by department (admin only)
    complaints_by_department = []
    if user.role == 'admin':
        complaints_by_department = list(
            queryset.values('department__name', 'department__code')
            .annotate(count=Count('id')).order_by('-count')
        )

    # Monthly trends (last 12 months)
    monthly_trends = []
    for i in range(12):
        month_start = timezone.now().replace(day=1) - timedelta(days=30*i)
        month_end = month_start.replace(day=28) + timedelta(days=4)
        month_end = month_end - timedelta(days=month_end.day)

        count = queryset.filter(
            created_at__gte=month_start,
            created_at__lte=month_end
        ).count()

        monthly_trends.append({
            'month': month_start.strftime('%Y-%m'),
            'count': count
        })

    monthly_trends.reverse()  # Show oldest first

    # Average resolution time (in days)
    resolved_queryset = queryset.filter(resolved_at__isnull=False)
    avg_resolution_time = 0
    if resolved_queryset.exists():
        from django.db.models import Avg, F
        avg_resolution_time = resolved_queryset.aggregate(
            avg_time=Avg(F('resolved_at') - F('created_at'))
        )['avg_time']
        if avg_resolution_time:
            avg_resolution_time = avg_resolution_time.days

    # Average satisfaction rating
    satisfaction_rating = 0
    feedback_queryset = ComplaintFeedback.objects.filter(complaint__in=queryset)
    if feedback_queryset.exists():
        satisfaction_rating = feedback_queryset.aggregate(
            avg_rating=Avg('satisfaction_rating')
        )['avg_rating'] or 0

    return Response({
        'total_complaints': total_complaints,
        'pending_complaints': pending_complaints,
        'resolved_complaints': resolved_complaints,
        'overdue_complaints': overdue_complaints,
        'complaints_by_status': complaints_by_status,
        'complaints_by_type': complaints_by_type,
        'complaints_by_department': complaints_by_department,
        'monthly_trends': monthly_trends,
        'average_resolution_time': avg_resolution_time,
        'satisfaction_rating': round(satisfaction_rating, 2)
    }, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def student_dashboard(request):
    """Get student dashboard statistics"""
    if request.user.role != 'student':
        return Response({
            'error': 'This endpoint is for students only.'
        }, status=status.HTTP_403_FORBIDDEN)

    user = request.user
    queryset = Complaint.objects.filter(complainant=user)

    # Basic counts
    total_complaints = queryset.count()
    pending_complaints = queryset.filter(status__in=['submitted', 'under_review', 'in_progress']).count()
    resolved_complaints = queryset.filter(status__in=['resolved', 'closed']).count()

    # Recent complaints
    recent_complaints = ComplaintListSerializer(
        queryset.order_by('-created_at')[:5], many=True
    ).data

    # Complaints by status
    complaints_by_status = list(
        queryset.values('status').annotate(count=Count('id'))
    )

    # Complaints by type
    complaints_by_type = list(
        queryset.values('complaint_type').annotate(count=Count('id'))
    )

    return Response({
        'total_complaints': total_complaints,
        'pending_complaints': pending_complaints,
        'resolved_complaints': resolved_complaints,
        'recent_complaints': recent_complaints,
        'complaints_by_status': complaints_by_status,
        'complaints_by_type': complaints_by_type
    }, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsAdminOrDepartmentOfficer])
def admin_dashboard(request):
    """Get comprehensive admin/department officer dashboard"""
    user = request.user

    # Base queryset based on user role
    if user.role == 'admin':
        complaint_queryset = Complaint.objects.all()
        user_queryset = User.objects.all()
        department_queryset = Department.objects.all()
    elif user.role == 'department_officer':
        complaint_queryset = Complaint.objects.filter(department=user.department)
        user_queryset = User.objects.filter(department=user.department)
        department_queryset = Department.objects.filter(id=user.department.id)
    else:
        return Response({
            'error': 'Permission denied.'
        }, status=status.HTTP_403_FORBIDDEN)

    # Complaint statistics
    complaint_stats = {
        'total': complaint_queryset.count(),
        'pending': complaint_queryset.filter(status__in=['submitted', 'under_review', 'in_progress']).count(),
        'resolved': complaint_queryset.filter(status__in=['resolved', 'closed']).count(),
        'overdue': complaint_queryset.filter(
            status__in=['submitted', 'under_review', 'in_progress'],
            created_at__lt=timezone.now() - timedelta(days=7)
        ).count(),
        'urgent': complaint_queryset.filter(is_urgent=True).count(),
    }

    # User statistics
    user_stats = {
        'total': user_queryset.count(),
        'active': user_queryset.filter(is_active=True).count(),
        'verified': user_queryset.filter(is_verified=True).count(),
        'students': user_queryset.filter(role='student').count(),
    }

    # Department statistics
    department_stats = {
        'total': department_queryset.count(),
        'active': department_queryset.filter(is_active=True).count(),
    }

    # Recent activity (last 7 days)
    seven_days_ago = timezone.now() - timedelta(days=7)
    recent_activity = {
        'new_complaints': complaint_queryset.filter(created_at__gte=seven_days_ago).count(),
        'resolved_complaints': complaint_queryset.filter(resolved_at__gte=seven_days_ago).count(),
        'new_users': user_queryset.filter(created_at__gte=seven_days_ago).count(),
    }

    # Top complaint types
    top_complaint_types = list(
        complaint_queryset.values('complaint_type')
        .annotate(count=Count('id'))
        .order_by('-count')[:5]
    )

    # Resolution trends (last 6 months)
    resolution_trends = []
    for i in range(6):
        month_start = timezone.now().replace(day=1) - timedelta(days=30*i)
        month_end = month_start.replace(day=28) + timedelta(days=4)
        month_end = month_end - timedelta(days=month_end.day)

        resolved_count = complaint_queryset.filter(
            resolved_at__gte=month_start,
            resolved_at__lte=month_end
        ).count()

        resolution_trends.append({
            'month': month_start.strftime('%Y-%m'),
            'resolved': resolved_count
        })

    resolution_trends.reverse()

    # Recent complaints
    recent_complaints = ComplaintListSerializer(
        complaint_queryset.order_by('-created_at')[:10], many=True
    ).data

    return Response({
        'complaints': complaint_stats,
        'users': user_stats,
        'departments': department_stats,
        'recent_activity': recent_activity,
        'top_complaint_types': top_complaint_types,
        'resolution_trends': resolution_trends,
        'recent_complaints': recent_complaints
    }, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([CanViewReports])
def complaint_reports(request):
    """Generate detailed complaint reports"""
    user = request.user

    # Base queryset based on user role
    if user.role == 'admin':
        queryset = Complaint.objects.all()
    elif user.role == 'department_officer':
        queryset = Complaint.objects.filter(department=user.department)
    else:
        return Response({
            'error': 'Permission denied.'
        }, status=status.HTTP_403_FORBIDDEN)

    # Apply filters
    date_from = request.query_params.get('date_from')
    date_to = request.query_params.get('date_to')
    status_filter = request.query_params.get('status')
    type_filter = request.query_params.get('type')
    department_filter = request.query_params.get('department')

    if date_from:
        queryset = queryset.filter(created_at__gte=date_from)
    if date_to:
        queryset = queryset.filter(created_at__lte=date_to)
    if status_filter:
        queryset = queryset.filter(status=status_filter)
    if type_filter:
        queryset = queryset.filter(complaint_type=type_filter)
    if department_filter and user.role == 'admin':
        queryset = queryset.filter(department__code=department_filter)

    # Generate report data
    report = {
        'summary': {
            'total_complaints': queryset.count(),
            'by_status': list(queryset.values('status').annotate(count=Count('id'))),
            'by_type': list(queryset.values('complaint_type').annotate(count=Count('id'))),
            'by_priority': list(queryset.values('priority').annotate(count=Count('id'))),
        },
        'performance': {
            'average_resolution_time': 0,
            'overdue_complaints': queryset.filter(
                status__in=['submitted', 'under_review', 'in_progress'],
                created_at__lt=timezone.now() - timedelta(days=7)
            ).count(),
            'satisfaction_rating': 0,
        },
        'trends': [],
        'filters_applied': {
            'date_from': date_from,
            'date_to': date_to,
            'status': status_filter,
            'type': type_filter,
            'department': department_filter,
        },
        'generated_at': timezone.now().isoformat(),
    }

    # Calculate average resolution time
    resolved_queryset = queryset.filter(resolved_at__isnull=False)
    if resolved_queryset.exists():
        from django.db.models import Avg, F
        avg_resolution_time = resolved_queryset.aggregate(
            avg_time=Avg(F('resolved_at') - F('created_at'))
        )['avg_time']
        if avg_resolution_time:
            report['performance']['average_resolution_time'] = avg_resolution_time.days

    # Calculate satisfaction rating
    feedback_queryset = ComplaintFeedback.objects.filter(complaint__in=queryset)
    if feedback_queryset.exists():
        avg_rating = feedback_queryset.aggregate(avg_rating=Avg('satisfaction_rating'))['avg_rating']
        report['performance']['satisfaction_rating'] = round(avg_rating or 0, 2)

    # Generate monthly trends
    for i in range(12):
        month_start = timezone.now().replace(day=1) - timedelta(days=30*i)
        month_end = month_start.replace(day=28) + timedelta(days=4)
        month_end = month_end - timedelta(days=month_end.day)

        month_complaints = queryset.filter(
            created_at__gte=month_start,
            created_at__lte=month_end
        ).count()

        month_resolved = queryset.filter(
            resolved_at__gte=month_start,
            resolved_at__lte=month_end
        ).count()

        report['trends'].append({
            'month': month_start.strftime('%Y-%m'),
            'complaints': month_complaints,
            'resolved': month_resolved
        })

    report['trends'].reverse()

    return Response(report, status=status.HTTP_200_OK)
