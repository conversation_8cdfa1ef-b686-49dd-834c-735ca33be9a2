from django.urls import path
from . import views
from . import file_views

app_name = 'complaints'

urlpatterns = [
    # Complaint CRUD endpoints
    path('', views.ComplaintListCreateView.as_view(), name='complaint_list_create'),
    path('<uuid:pk>/', views.ComplaintDetailView.as_view(), name='complaint_detail'),

    # Complaint comments
    path('<uuid:complaint_id>/comments/', views.ComplaintCommentListCreateView.as_view(), name='complaint_comments'),

    # Complaint feedback
    path('<uuid:complaint_id>/feedback/', views.ComplaintFeedbackView.as_view(), name='complaint_feedback'),

    # Complaint management
    path('<uuid:complaint_id>/assign/', views.assign_complaint, name='assign_complaint'),
    path('<uuid:complaint_id>/status/', views.update_complaint_status, name='update_complaint_status'),

    # Analytics and dashboard endpoints
    path('analytics/', views.complaint_analytics, name='complaint_analytics'),
    path('reports/', views.complaint_reports, name='complaint_reports'),
    path('dashboard/student/', views.student_dashboard, name='student_dashboard'),
    path('dashboard/admin/', views.admin_dashboard, name='admin_dashboard'),

    # File management endpoints
    path('<uuid:complaint_id>/attachments/', file_views.ComplaintAttachmentListView.as_view(), name='complaint_attachments'),
    path('<uuid:complaint_id>/attachments/<uuid:pk>/', file_views.ComplaintAttachmentDetailView.as_view(), name='complaint_attachment_detail'),
    path('<uuid:complaint_id>/attachments/<uuid:attachment_id>/download/', file_views.download_attachment, name='download_attachment'),
    path('<uuid:complaint_id>/attachments/<uuid:attachment_id>/preview/', file_views.preview_attachment, name='preview_attachment'),

    # File utilities
    path('files/upload-info/', file_views.file_upload_info, name='file_upload_info'),
    path('files/validate/', file_views.validate_file_upload, name='validate_file_upload'),
    path('files/stats/', file_views.FileManagementStatsView.as_view(), name='file_management_stats'),
]
