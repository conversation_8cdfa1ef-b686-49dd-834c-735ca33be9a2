from django.urls import path
from . import views

app_name = 'notifications'

urlpatterns = [
    # User notification endpoints
    path('', views.NotificationListView.as_view(), name='notification_list'),
    path('<uuid:pk>/', views.NotificationDetailView.as_view(), name='notification_detail'),
    path('<uuid:notification_id>/read/', views.mark_notification_read, name='mark_notification_read'),
    path('mark-all-read/', views.mark_all_notifications_read, name='mark_all_notifications_read'),
    path('summary/', views.notification_summary, name='notification_summary'),

    # User preferences
    path('preferences/', views.NotificationPreferenceView.as_view(), name='notification_preferences'),

    # Admin notification management
    path('admin/', views.AdminNotificationListView.as_view(), name='admin_notification_list'),
    path('admin/bulk/', views.send_bulk_notification, name='send_bulk_notification'),
    path('admin/daily-digest/', views.send_daily_digest, name='send_daily_digest'),
    path('admin/analytics/', views.notification_analytics, name='notification_analytics'),
    path('admin/test/', views.test_notification_system, name='test_notification_system'),

    # Email template management
    path('admin/templates/', views.EmailTemplateListCreateView.as_view(), name='email_template_list'),
    path('admin/templates/<uuid:pk>/', views.EmailTemplateDetailView.as_view(), name='email_template_detail'),

    # Notification logs
    path('admin/logs/', views.NotificationLogListView.as_view(), name='notification_log_list'),
]
