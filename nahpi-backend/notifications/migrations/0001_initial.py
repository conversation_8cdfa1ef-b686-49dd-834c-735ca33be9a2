# Generated by Django 5.2.4 on 2025-07-06 10:37

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('complaints', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, unique=True)),
                ('notification_type', models.CharField(choices=[('complaint_submitted', 'Complaint Submitted'), ('complaint_updated', 'Complaint Updated'), ('complaint_resolved', 'Complaint Resolved'), ('complaint_assigned', 'Complaint Assigned'), ('status_changed', 'Status Changed'), ('new_comment', 'New Comment'), ('feedback_request', 'Feedback Request'), ('system_announcement', 'System Announcement'), ('account_verification', 'Account Verification'), ('password_reset', 'Password Reset')], max_length=30)),
                ('subject', models.CharField(max_length=200)),
                ('html_content', models.TextField()),
                ('text_content', models.TextField(blank=True)),
                ('variables', models.JSONField(blank=True, default=dict)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Email Template',
                'verbose_name_plural': 'Email Templates',
                'db_table': 'notifications_emailtemplate',
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('notification_type', models.CharField(choices=[('complaint_submitted', 'Complaint Submitted'), ('complaint_updated', 'Complaint Updated'), ('complaint_resolved', 'Complaint Resolved'), ('complaint_assigned', 'Complaint Assigned'), ('status_changed', 'Status Changed'), ('new_comment', 'New Comment'), ('feedback_request', 'Feedback Request'), ('system_announcement', 'System Announcement'), ('account_verification', 'Account Verification'), ('password_reset', 'Password Reset')], max_length=30)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10)),
                ('is_read', models.BooleanField(default=False)),
                ('is_sent', models.BooleanField(default=False)),
                ('send_email', models.BooleanField(default=True)),
                ('send_sms', models.BooleanField(default=False)),
                ('send_push', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('action_url', models.URLField(blank=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('complaint', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='complaints.complaint')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification',
                'verbose_name_plural': 'Notifications',
                'db_table': 'notifications_notification',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='NotificationLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('channel', models.CharField(max_length=20)),
                ('recipient_address', models.CharField(max_length=255)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('delivered', 'Delivered'), ('failed', 'Failed'), ('bounced', 'Bounced')], default='pending', max_length=20)),
                ('external_id', models.CharField(blank=True, max_length=100)),
                ('response_data', models.JSONField(blank=True, default=dict)),
                ('error_message', models.TextField(blank=True)),
                ('sent_at', models.DateTimeField(auto_now_add=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('notification', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='delivery_logs', to='notifications.notification')),
            ],
            options={
                'verbose_name': 'Notification Log',
                'verbose_name_plural': 'Notification Logs',
                'db_table': 'notifications_log',
                'ordering': ['-sent_at'],
            },
        ),
        migrations.CreateModel(
            name='NotificationPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_complaint_updates', models.BooleanField(default=True)),
                ('email_status_changes', models.BooleanField(default=True)),
                ('email_new_comments', models.BooleanField(default=True)),
                ('email_resolution_notifications', models.BooleanField(default=True)),
                ('email_daily_digest', models.BooleanField(default=True)),
                ('email_weekly_summary', models.BooleanField(default=False)),
                ('sms_urgent_notifications', models.BooleanField(default=False)),
                ('sms_resolution_notifications', models.BooleanField(default=False)),
                ('push_complaint_updates', models.BooleanField(default=True)),
                ('push_status_changes', models.BooleanField(default=True)),
                ('push_new_comments', models.BooleanField(default=True)),
                ('daily_digest_time', models.TimeField(default='09:00:00')),
                ('weekly_digest_day', models.IntegerField(default=1)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification Preference',
                'verbose_name_plural': 'Notification Preferences',
                'db_table': 'notifications_preference',
            },
        ),
    ]
