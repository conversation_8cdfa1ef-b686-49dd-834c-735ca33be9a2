from django.db import models
from django.conf import settings
import uuid

class Notification(models.Model):
    """
    System notifications for users
    """

    NOTIFICATION_TYPES = [
        ('complaint_submitted', 'Complaint Submitted'),
        ('complaint_updated', 'Complaint Updated'),
        ('complaint_resolved', 'Complaint Resolved'),
        ('complaint_assigned', 'Complaint Assigned'),
        ('status_changed', 'Status Changed'),
        ('new_comment', 'New Comment'),
        ('feedback_request', 'Feedback Request'),
        ('system_announcement', 'System Announcement'),
        ('account_verification', 'Account Verification'),
        ('password_reset', 'Password Reset'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Recipient
    recipient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='notifications'
    )

    # Notification Content
    title = models.CharField(max_length=200)
    message = models.TextField()
    notification_type = models.CharField(max_length=30, choices=NOTIFICATION_TYPES)
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='medium')

    # Related Objects
    complaint = models.ForeignKey(
        'complaints.Complaint',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='notifications'
    )

    # Status
    is_read = models.BooleanField(default=False)
    is_sent = models.BooleanField(default=False)

    # Delivery Channels
    send_email = models.BooleanField(default=True)
    send_sms = models.BooleanField(default=False)
    send_push = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)
    sent_at = models.DateTimeField(null=True, blank=True)

    # Additional Data
    action_url = models.URLField(blank=True)  # URL for action buttons
    metadata = models.JSONField(default=dict, blank=True)  # Additional data

    class Meta:
        db_table = 'notifications_notification'
        verbose_name = 'Notification'
        verbose_name_plural = 'Notifications'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.recipient.email}"

    def mark_as_read(self):
        """Mark notification as read"""
        if not self.is_read:
            from django.utils import timezone
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])

    def mark_as_sent(self):
        """Mark notification as sent"""
        if not self.is_sent:
            from django.utils import timezone
            self.is_sent = True
            self.sent_at = timezone.now()
            self.save(update_fields=['is_sent', 'sent_at'])


class EmailTemplate(models.Model):
    """
    Email templates for different notification types
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    name = models.CharField(max_length=100, unique=True)
    notification_type = models.CharField(max_length=30, choices=Notification.NOTIFICATION_TYPES)

    subject = models.CharField(max_length=200)
    html_content = models.TextField()
    text_content = models.TextField(blank=True)

    # Template variables documentation
    variables = models.JSONField(default=dict, blank=True)  # Available template variables

    # Status
    is_active = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'notifications_emailtemplate'
        verbose_name = 'Email Template'
        verbose_name_plural = 'Email Templates'

    def __str__(self):
        return f"{self.name} - {self.get_notification_type_display()}"


class NotificationPreference(models.Model):
    """
    User notification preferences
    """
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='notification_preferences'
    )

    # Email Preferences
    email_complaint_updates = models.BooleanField(default=True)
    email_status_changes = models.BooleanField(default=True)
    email_new_comments = models.BooleanField(default=True)
    email_resolution_notifications = models.BooleanField(default=True)
    email_daily_digest = models.BooleanField(default=True)
    email_weekly_summary = models.BooleanField(default=False)

    # SMS Preferences
    sms_urgent_notifications = models.BooleanField(default=False)
    sms_resolution_notifications = models.BooleanField(default=False)

    # Push Notification Preferences
    push_complaint_updates = models.BooleanField(default=True)
    push_status_changes = models.BooleanField(default=True)
    push_new_comments = models.BooleanField(default=True)

    # Digest Preferences
    daily_digest_time = models.TimeField(default='09:00:00')  # 9 AM
    weekly_digest_day = models.IntegerField(default=1)  # Monday (1=Monday, 7=Sunday)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'notifications_preference'
        verbose_name = 'Notification Preference'
        verbose_name_plural = 'Notification Preferences'

    def __str__(self):
        return f"Preferences for {self.user.email}"


class NotificationLog(models.Model):
    """
    Log of sent notifications for tracking and debugging
    """
    DELIVERY_STATUS = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('bounced', 'Bounced'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    notification = models.ForeignKey(
        Notification,
        on_delete=models.CASCADE,
        related_name='delivery_logs'
    )

    # Delivery Details
    channel = models.CharField(max_length=20)  # email, sms, push
    recipient_address = models.CharField(max_length=255)  # email address, phone number, etc.
    status = models.CharField(max_length=20, choices=DELIVERY_STATUS, default='pending')

    # Response Details
    external_id = models.CharField(max_length=100, blank=True)  # ID from email/SMS service
    response_data = models.JSONField(default=dict, blank=True)
    error_message = models.TextField(blank=True)

    # Timestamps
    sent_at = models.DateTimeField(auto_now_add=True)
    delivered_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'notifications_log'
        verbose_name = 'Notification Log'
        verbose_name_plural = 'Notification Logs'
        ordering = ['-sent_at']

    def __str__(self):
        return f"{self.channel} to {self.recipient_address} - {self.status}"
