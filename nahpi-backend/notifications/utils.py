from django.core.mail import send_mail, send_mass_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils import timezone
from datetime import timedelta
from .models import Notification, EmailTemplate, NotificationLog, NotificationPreference
from accounts.models import User
from complaints.models import Complaint


def create_notification(recipient, title, message, notification_type, **kwargs):
    """Create a new notification"""
    notification = Notification.objects.create(
        recipient=recipient,
        title=title,
        message=message,
        notification_type=notification_type,
        priority=kwargs.get('priority', 'medium'),
        complaint=kwargs.get('complaint'),
        send_email=kwargs.get('send_email', True),
        send_sms=kwargs.get('send_sms', False),
        send_push=kwargs.get('send_push', True),
        action_url=kwargs.get('action_url', ''),
        metadata=kwargs.get('metadata', {})
    )
    
    # Send notification immediately
    send_notification(notification)
    
    return notification


def send_notification(notification):
    """Send notification through configured channels"""
    user_preferences = get_user_notification_preferences(notification.recipient)
    
    # Check if user wants this type of notification
    if not should_send_notification(notification, user_preferences):
        return
    
    # Send email
    if notification.send_email and user_preferences.get('email_enabled', True):
        send_email_notification(notification)
    
    # Send SMS
    if notification.send_sms and user_preferences.get('sms_enabled', False):
        send_sms_notification(notification)
    
    # Send push notification
    if notification.send_push and user_preferences.get('push_enabled', True):
        send_push_notification(notification)
    
    # Mark as sent
    notification.mark_as_sent()


def send_email_notification(notification):
    """Send email notification"""
    try:
        # Get email template if available
        template = get_email_template(notification.notification_type)
        
        if template:
            subject = render_template_string(template.subject, notification)
            html_content = render_template_string(template.html_content, notification)
            text_content = render_template_string(template.text_content, notification) if template.text_content else None
        else:
            subject = notification.title
            html_content = create_default_email_html(notification)
            text_content = notification.message
        
        # Send email
        send_mail(
            subject=subject,
            message=text_content or notification.message,
            html_message=html_content,
            from_email=settings.EMAIL_HOST_USER,
            recipient_list=[notification.recipient.email],
            fail_silently=False
        )
        
        # Log successful delivery
        NotificationLog.objects.create(
            notification=notification,
            channel='email',
            recipient_address=notification.recipient.email,
            status='sent'
        )
        
    except Exception as e:
        # Log failed delivery
        NotificationLog.objects.create(
            notification=notification,
            channel='email',
            recipient_address=notification.recipient.email,
            status='failed',
            error_message=str(e)
        )


def send_sms_notification(notification):
    """Send SMS notification (placeholder for SMS service integration)"""
    try:
        # In production, integrate with SMS service like Twilio, AWS SNS, etc.
        phone_number = notification.recipient.phone_number
        message = f"{notification.title}: {notification.message}"
        
        # Placeholder SMS sending
        print(f"SMS to {phone_number}: {message}")
        
        # Log successful delivery
        NotificationLog.objects.create(
            notification=notification,
            channel='sms',
            recipient_address=phone_number,
            status='sent'
        )
        
    except Exception as e:
        # Log failed delivery
        NotificationLog.objects.create(
            notification=notification,
            channel='sms',
            recipient_address=notification.recipient.phone_number,
            status='failed',
            error_message=str(e)
        )


def send_push_notification(notification):
    """Send push notification (placeholder for push service integration)"""
    try:
        # In production, integrate with push notification service
        # like Firebase Cloud Messaging, Apple Push Notification Service, etc.
        
        push_data = {
            'title': notification.title,
            'message': notification.message,
            'action_url': notification.action_url,
            'metadata': notification.metadata
        }
        
        # Placeholder push notification sending
        print(f"Push notification to {notification.recipient.email}: {push_data}")
        
        # Log successful delivery
        NotificationLog.objects.create(
            notification=notification,
            channel='push',
            recipient_address=notification.recipient.email,
            status='sent'
        )
        
    except Exception as e:
        # Log failed delivery
        NotificationLog.objects.create(
            notification=notification,
            channel='push',
            recipient_address=notification.recipient.email,
            status='failed',
            error_message=str(e)
        )


def get_user_notification_preferences(user):
    """Get user notification preferences"""
    try:
        preferences = NotificationPreference.objects.get(user=user)
        return {
            'email_enabled': preferences.email_complaint_updates,
            'sms_enabled': preferences.sms_urgent_notifications,
            'push_enabled': preferences.push_complaint_updates,
            'email_status_changes': preferences.email_status_changes,
            'email_new_comments': preferences.email_new_comments,
            'email_resolution_notifications': preferences.email_resolution_notifications,
        }
    except NotificationPreference.DoesNotExist:
        # Return default preferences
        return {
            'email_enabled': True,
            'sms_enabled': False,
            'push_enabled': True,
            'email_status_changes': True,
            'email_new_comments': True,
            'email_resolution_notifications': True,
        }


def should_send_notification(notification, user_preferences):
    """Check if notification should be sent based on user preferences"""
    notification_type = notification.notification_type
    
    # Map notification types to preference settings
    type_preference_map = {
        'complaint_submitted': 'email_enabled',
        'complaint_updated': 'email_enabled',
        'status_changed': 'email_status_changes',
        'new_comment': 'email_new_comments',
        'complaint_resolved': 'email_resolution_notifications',
        'system_announcement': 'email_enabled',
    }
    
    preference_key = type_preference_map.get(notification_type, 'email_enabled')
    return user_preferences.get(preference_key, True)


def get_email_template(notification_type):
    """Get email template for notification type"""
    try:
        return EmailTemplate.objects.get(
            notification_type=notification_type,
            is_active=True
        )
    except EmailTemplate.DoesNotExist:
        return None


def render_template_string(template_string, notification):
    """Render template string with notification context"""
    context = {
        'user': notification.recipient,
        'notification': notification,
        'complaint': notification.complaint,
        'site_name': 'NAHPi Complains',
        'site_url': settings.CORS_ALLOWED_ORIGINS[0] if settings.CORS_ALLOWED_ORIGINS else 'http://localhost:3000'
    }
    
    # Simple template variable replacement
    for key, value in context.items():
        if value:
            template_string = template_string.replace(f'{{{key}}}', str(value))
            if hasattr(value, '__dict__'):
                for attr_name, attr_value in value.__dict__.items():
                    if not attr_name.startswith('_'):
                        template_string = template_string.replace(f'{{{key}.{attr_name}}}', str(attr_value))
    
    return template_string


def create_default_email_html(notification):
    """Create default HTML email content"""
    html_content = f"""
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #08387F;">{notification.title}</h2>
            <p>{notification.message}</p>
            
            {f'<p><strong>Complaint:</strong> {notification.complaint.complaint_number}</p>' if notification.complaint else ''}
            
            {f'<p><a href="{notification.action_url}" style="background-color: #08387F; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">View Details</a></p>' if notification.action_url else ''}
            
            <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
            <p style="font-size: 12px; color: #666;">
                This is an automated message from NAHPi Complains System.<br>
                If you no longer wish to receive these notifications, you can update your preferences in your account settings.
            </p>
        </div>
    </body>
    </html>
    """
    return html_content


def send_daily_digest_to_department_officers():
    """Send daily digest emails to department officers"""
    from django.db.models import Count, Q
    
    # Get all active department officers
    officers = User.objects.filter(
        role='department_officer',
        is_active=True,
        department__isnull=False
    ).select_related('department')
    
    count = 0
    
    for officer in officers:
        # Check if user wants daily digest
        preferences = get_user_notification_preferences(officer)
        if not preferences.get('email_enabled', True):
            continue
        
        # Get complaints for this officer's department
        complaints = Complaint.objects.filter(department=officer.department)
        
        # Calculate statistics
        total_complaints = complaints.count()
        pending_complaints = complaints.filter(
            status__in=['submitted', 'under_review', 'in_progress']
        ).count()
        overdue_complaints = complaints.filter(
            status__in=['submitted', 'under_review', 'in_progress'],
            created_at__lt=timezone.now() - timedelta(days=7)
        ).count()
        
        # Get new complaints from yesterday
        yesterday = timezone.now() - timedelta(days=1)
        new_complaints = complaints.filter(created_at__gte=yesterday)
        
        # Only send if there's activity or overdue complaints
        if new_complaints.exists() or overdue_complaints > 0:
            # Create notification
            create_notification(
                recipient=officer,
                title=f'Daily Complaint Digest - {officer.department.name}',
                message=f'You have {pending_complaints} pending complaints, {overdue_complaints} overdue complaints, and {new_complaints.count()} new complaints.',
                notification_type='daily_digest',
                priority='low',
                metadata={
                    'total_complaints': total_complaints,
                    'pending_complaints': pending_complaints,
                    'overdue_complaints': overdue_complaints,
                    'new_complaints': new_complaints.count()
                }
            )
            count += 1
    
    return count


def send_complaint_notification(complaint, notification_type, additional_recipients=None):
    """Send complaint-related notifications"""
    recipients = []
    
    if notification_type == 'complaint_submitted':
        # Notify department officers
        recipients = User.objects.filter(
            role='department_officer',
            department=complaint.department,
            is_active=True
        )
        title = f'New Complaint Submitted - {complaint.complaint_number}'
        message = f'A new complaint "{complaint.title}" has been submitted to your department.'
        
    elif notification_type == 'status_changed':
        # Notify complainant
        recipients = [complaint.complainant]
        title = f'Complaint Status Updated - {complaint.complaint_number}'
        message = f'Your complaint status has been updated to {complaint.get_status_display()}.'
        
    elif notification_type == 'complaint_resolved':
        # Notify complainant
        recipients = [complaint.complainant]
        title = f'Complaint Resolved - {complaint.complaint_number}'
        message = f'Your complaint has been resolved. Please provide feedback on your experience.'
        
    elif notification_type == 'complaint_assigned':
        # Notify assigned user
        if complaint.assigned_to:
            recipients = [complaint.assigned_to]
            title = f'Complaint Assigned - {complaint.complaint_number}'
            message = f'A complaint has been assigned to you: "{complaint.title}"'
    
    # Add additional recipients if provided
    if additional_recipients:
        recipients.extend(additional_recipients)
    
    # Create notifications
    for recipient in recipients:
        create_notification(
            recipient=recipient,
            title=title,
            message=message,
            notification_type=notification_type,
            complaint=complaint,
            action_url=f'/complaints/{complaint.id}',
            priority='high' if complaint.is_urgent else 'medium'
        )
