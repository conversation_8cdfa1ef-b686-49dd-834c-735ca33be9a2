from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Notification, EmailTemplate, NotificationPreference, NotificationLog

User = get_user_model()


class NotificationSerializer(serializers.ModelSerializer):
    """Serializer for notifications"""
    recipient_name = serializers.CharField(source='recipient.full_name', read_only=True)
    complaint_number = serializers.CharField(source='complaint.complaint_number', read_only=True)
    
    class Meta:
        model = Notification
        fields = [
            'id', 'title', 'message', 'notification_type', 'priority',
            'recipient', 'recipient_name', 'complaint', 'complaint_number',
            'is_read', 'is_sent', 'send_email', 'send_sms', 'send_push',
            'action_url', 'metadata', 'created_at', 'read_at', 'sent_at'
        ]
        read_only_fields = ['id', 'is_sent', 'sent_at', 'created_at', 'read_at']


class NotificationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating notifications"""
    recipient_ids = serializers.ListField(
        child=serializers.UUIDField(),
        write_only=True,
        required=False
    )
    send_to_all = serializers.BooleanField(write_only=True, default=False)
    send_to_role = serializers.ChoiceField(
        choices=[('student', 'Student'), ('admin', 'Admin'), ('department_officer', 'Department Officer')],
        write_only=True,
        required=False
    )
    
    class Meta:
        model = Notification
        fields = [
            'title', 'message', 'notification_type', 'priority',
            'complaint', 'send_email', 'send_sms', 'send_push',
            'action_url', 'metadata', 'recipient_ids', 'send_to_all', 'send_to_role'
        ]
    
    def create(self, validated_data):
        recipient_ids = validated_data.pop('recipient_ids', [])
        send_to_all = validated_data.pop('send_to_all', False)
        send_to_role = validated_data.pop('send_to_role', None)
        
        notifications = []
        
        # Determine recipients
        if send_to_all:
            recipients = User.objects.filter(is_active=True)
        elif send_to_role:
            recipients = User.objects.filter(role=send_to_role, is_active=True)
        elif recipient_ids:
            recipients = User.objects.filter(id__in=recipient_ids, is_active=True)
        else:
            raise serializers.ValidationError("Must specify recipients.")
        
        # Create notifications for each recipient
        for recipient in recipients:
            notification = Notification.objects.create(
                recipient=recipient,
                **validated_data
            )
            notifications.append(notification)
        
        return notifications[0] if notifications else None


class NotificationPreferenceSerializer(serializers.ModelSerializer):
    """Serializer for notification preferences"""
    
    class Meta:
        model = NotificationPreference
        fields = [
            'email_complaint_updates', 'email_status_changes', 'email_new_comments',
            'email_resolution_notifications', 'email_daily_digest', 'email_weekly_summary',
            'sms_urgent_notifications', 'sms_resolution_notifications',
            'push_complaint_updates', 'push_status_changes', 'push_new_comments',
            'daily_digest_time', 'weekly_digest_day'
        ]


class EmailTemplateSerializer(serializers.ModelSerializer):
    """Serializer for email templates"""
    
    class Meta:
        model = EmailTemplate
        fields = [
            'id', 'name', 'notification_type', 'subject', 'html_content',
            'text_content', 'variables', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class NotificationLogSerializer(serializers.ModelSerializer):
    """Serializer for notification logs"""
    notification_title = serializers.CharField(source='notification.title', read_only=True)
    
    class Meta:
        model = NotificationLog
        fields = [
            'id', 'notification', 'notification_title', 'channel', 'recipient_address',
            'status', 'external_id', 'response_data', 'error_message',
            'sent_at', 'delivered_at'
        ]
        read_only_fields = ['id', 'sent_at', 'delivered_at']


class BulkNotificationSerializer(serializers.Serializer):
    """Serializer for bulk notifications"""
    title = serializers.CharField(max_length=200)
    message = serializers.CharField()
    notification_type = serializers.ChoiceField(choices=Notification.NOTIFICATION_TYPES)
    priority = serializers.ChoiceField(choices=Notification.PRIORITY_LEVELS, default='medium')
    
    # Recipient selection
    recipient_ids = serializers.ListField(
        child=serializers.UUIDField(),
        required=False
    )
    send_to_all = serializers.BooleanField(default=False)
    send_to_role = serializers.ChoiceField(
        choices=[('student', 'Student'), ('admin', 'Admin'), ('department_officer', 'Department Officer')],
        required=False
    )
    send_to_department = serializers.CharField(max_length=10, required=False)
    
    # Delivery options
    send_email = serializers.BooleanField(default=True)
    send_sms = serializers.BooleanField(default=False)
    send_push = serializers.BooleanField(default=True)
    
    # Optional fields
    action_url = serializers.URLField(required=False)
    metadata = serializers.JSONField(default=dict)
    
    def validate(self, attrs):
        """Validate recipient selection"""
        recipient_ids = attrs.get('recipient_ids', [])
        send_to_all = attrs.get('send_to_all', False)
        send_to_role = attrs.get('send_to_role')
        send_to_department = attrs.get('send_to_department')
        
        if not any([recipient_ids, send_to_all, send_to_role, send_to_department]):
            raise serializers.ValidationError("Must specify at least one recipient option.")
        
        return attrs
    
    def create(self, validated_data):
        """Create bulk notifications"""
        recipient_ids = validated_data.pop('recipient_ids', [])
        send_to_all = validated_data.pop('send_to_all', False)
        send_to_role = validated_data.pop('send_to_role', None)
        send_to_department = validated_data.pop('send_to_department', None)
        
        # Determine recipients
        recipients = User.objects.filter(is_active=True)
        
        if send_to_all:
            pass  # Use all active users
        elif send_to_role:
            recipients = recipients.filter(role=send_to_role)
        elif send_to_department:
            recipients = recipients.filter(department__code=send_to_department)
        elif recipient_ids:
            recipients = recipients.filter(id__in=recipient_ids)
        
        # Create notifications
        notifications = []
        for recipient in recipients:
            notification = Notification.objects.create(
                recipient=recipient,
                **validated_data
            )
            notifications.append(notification)
        
        return {
            'count': len(notifications),
            'notifications': notifications
        }


class NotificationStatsSerializer(serializers.Serializer):
    """Serializer for notification statistics"""
    total_notifications = serializers.IntegerField()
    sent_notifications = serializers.IntegerField()
    pending_notifications = serializers.IntegerField()
    failed_notifications = serializers.IntegerField()
    notifications_by_type = serializers.ListField()
    notifications_by_priority = serializers.ListField()
    delivery_stats = serializers.DictField()
    recent_activity = serializers.DictField()


class DailyDigestSerializer(serializers.Serializer):
    """Serializer for daily digest data"""
    user = serializers.CharField(source='user.full_name')
    department = serializers.CharField(source='user.department.name')
    new_complaints = serializers.IntegerField()
    pending_complaints = serializers.IntegerField()
    overdue_complaints = serializers.IntegerField()
    resolved_complaints = serializers.IntegerField()
    complaints_summary = serializers.ListField()
    generated_at = serializers.DateTimeField()
