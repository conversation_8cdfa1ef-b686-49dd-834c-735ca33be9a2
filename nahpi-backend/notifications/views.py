from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Q, Count
from django.utils import timezone
from datetime import datetime, timedelta

from .models import Notification, EmailTemplate, NotificationPreference, NotificationLog
from .serializers import (
    NotificationSerializer, NotificationCreateSerializer, EmailTemplateSerializer,
    NotificationPreferenceSerializer, NotificationLogSerializer, BulkNotificationSerializer
)
from accounts.permissions import IsAdmin, IsAdminOrDepartmentOfficer
from accounts.models import User


class NotificationListView(generics.ListAPIView):
    """List user notifications"""
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        queryset = Notification.objects.filter(recipient=user)

        # Filter by read status
        is_read = self.request.query_params.get('is_read')
        if is_read is not None:
            queryset = queryset.filter(is_read=is_read.lower() == 'true')

        # Filter by notification type
        notification_type = self.request.query_params.get('type')
        if notification_type:
            queryset = queryset.filter(notification_type=notification_type)

        # Filter by priority
        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)

        return queryset.order_by('-created_at')


class NotificationDetailView(generics.RetrieveUpdateAPIView):
    """Retrieve and update notification"""
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Notification.objects.filter(recipient=self.request.user)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()

        # Mark as read when retrieved
        if not instance.is_read:
            instance.mark_as_read()

        serializer = self.get_serializer(instance)
        return Response(serializer.data)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def mark_notification_read(request, notification_id):
    """Mark a notification as read"""
    try:
        notification = Notification.objects.get(
            id=notification_id,
            recipient=request.user
        )
        notification.mark_as_read()

        return Response({
            'message': 'Notification marked as read.'
        }, status=status.HTTP_200_OK)

    except Notification.DoesNotExist:
        return Response({
            'error': 'Notification not found.'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def mark_all_notifications_read(request):
    """Mark all user notifications as read"""
    notifications = Notification.objects.filter(
        recipient=request.user,
        is_read=False
    )

    count = notifications.count()

    for notification in notifications:
        notification.mark_as_read()

    return Response({
        'message': f'{count} notifications marked as read.'
    }, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def notification_summary(request):
    """Get notification summary for user"""
    user = request.user

    total_notifications = Notification.objects.filter(recipient=user).count()
    unread_notifications = Notification.objects.filter(recipient=user, is_read=False).count()

    # Recent notifications (last 7 days)
    seven_days_ago = timezone.now() - timedelta(days=7)
    recent_notifications = Notification.objects.filter(
        recipient=user,
        created_at__gte=seven_days_ago
    ).count()

    # Notifications by type
    notifications_by_type = list(
        Notification.objects.filter(recipient=user)
        .values('notification_type')
        .annotate(count=Count('id'))
        .order_by('-count')
    )

    # Notifications by priority
    notifications_by_priority = list(
        Notification.objects.filter(recipient=user)
        .values('priority')
        .annotate(count=Count('id'))
    )

    return Response({
        'total_notifications': total_notifications,
        'unread_notifications': unread_notifications,
        'recent_notifications': recent_notifications,
        'notifications_by_type': notifications_by_type,
        'notifications_by_priority': notifications_by_priority
    }, status=status.HTTP_200_OK)


class NotificationPreferenceView(generics.RetrieveUpdateAPIView):
    """Get and update user notification preferences"""
    serializer_class = NotificationPreferenceSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        preference, created = NotificationPreference.objects.get_or_create(
            user=self.request.user
        )
        return preference

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        preference = serializer.save()

        return Response({
            'message': 'Notification preferences updated successfully.',
            'preferences': NotificationPreferenceSerializer(preference).data
        }, status=status.HTTP_200_OK)


# ============================================================================
# ADMIN NOTIFICATION MANAGEMENT
# ============================================================================

class AdminNotificationListView(generics.ListCreateAPIView):
    """Admin view to list and create notifications"""
    queryset = Notification.objects.all()
    permission_classes = [IsAdmin]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return NotificationCreateSerializer
        return NotificationSerializer

    def get_queryset(self):
        queryset = Notification.objects.all().select_related('recipient', 'complaint')

        # Filter by recipient
        recipient_id = self.request.query_params.get('recipient')
        if recipient_id:
            queryset = queryset.filter(recipient_id=recipient_id)

        # Filter by type
        notification_type = self.request.query_params.get('type')
        if notification_type:
            queryset = queryset.filter(notification_type=notification_type)

        # Filter by sent status
        is_sent = self.request.query_params.get('is_sent')
        if is_sent is not None:
            queryset = queryset.filter(is_sent=is_sent.lower() == 'true')

        return queryset.order_by('-created_at')

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        notification = serializer.save()

        return Response({
            'message': 'Notification created successfully.',
            'notification': NotificationSerializer(notification).data
        }, status=status.HTTP_201_CREATED)


@api_view(['POST'])
@permission_classes([IsAdmin])
def send_bulk_notification(request):
    """Send bulk notifications to multiple users"""
    serializer = BulkNotificationSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    result = serializer.save()

    return Response({
        'message': f'Bulk notification sent to {result["count"]} users successfully.',
        'count': result['count']
    }, status=status.HTTP_201_CREATED)


@api_view(['POST'])
@permission_classes([IsAdminOrDepartmentOfficer])
def send_daily_digest(request):
    """Manually trigger daily digest emails"""
    from .utils import send_daily_digest_to_department_officers

    try:
        count = send_daily_digest_to_department_officers()
        return Response({
            'message': f'Daily digest sent to {count} department officers.'
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({
            'error': f'Failed to send daily digest: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAdmin])
def notification_analytics(request):
    """Get notification analytics and statistics"""
    from django.db.models import Count, Q
    from datetime import timedelta

    # Basic counts
    total_notifications = Notification.objects.count()
    sent_notifications = Notification.objects.filter(is_sent=True).count()
    pending_notifications = Notification.objects.filter(is_sent=False).count()

    # Failed notifications (from logs)
    failed_notifications = NotificationLog.objects.filter(status='failed').count()

    # Notifications by type
    notifications_by_type = list(
        Notification.objects.values('notification_type')
        .annotate(count=Count('id'))
        .order_by('-count')
    )

    # Notifications by priority
    notifications_by_priority = list(
        Notification.objects.values('priority')
        .annotate(count=Count('id'))
    )

    # Delivery statistics
    delivery_stats = {
        'email': NotificationLog.objects.filter(channel='email').count(),
        'sms': NotificationLog.objects.filter(channel='sms').count(),
        'push': NotificationLog.objects.filter(channel='push').count(),
        'delivered': NotificationLog.objects.filter(status='delivered').count(),
        'failed': NotificationLog.objects.filter(status='failed').count(),
    }

    # Recent activity (last 7 days)
    seven_days_ago = timezone.now() - timedelta(days=7)
    recent_activity = {
        'notifications_sent': Notification.objects.filter(
            created_at__gte=seven_days_ago
        ).count(),
        'emails_sent': NotificationLog.objects.filter(
            channel='email',
            sent_at__gte=seven_days_ago
        ).count(),
    }

    return Response({
        'total_notifications': total_notifications,
        'sent_notifications': sent_notifications,
        'pending_notifications': pending_notifications,
        'failed_notifications': failed_notifications,
        'notifications_by_type': notifications_by_type,
        'notifications_by_priority': notifications_by_priority,
        'delivery_stats': delivery_stats,
        'recent_activity': recent_activity
    }, status=status.HTTP_200_OK)


class EmailTemplateListCreateView(generics.ListCreateAPIView):
    """List and create email templates"""
    queryset = EmailTemplate.objects.all()
    serializer_class = EmailTemplateSerializer
    permission_classes = [IsAdmin]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        template = serializer.save()

        return Response({
            'message': 'Email template created successfully.',
            'template': EmailTemplateSerializer(template).data
        }, status=status.HTTP_201_CREATED)


class EmailTemplateDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete email template"""
    queryset = EmailTemplate.objects.all()
    serializer_class = EmailTemplateSerializer
    permission_classes = [IsAdmin]

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        template = serializer.save()

        return Response({
            'message': 'Email template updated successfully.',
            'template': EmailTemplateSerializer(template).data
        }, status=status.HTTP_200_OK)


class NotificationLogListView(generics.ListAPIView):
    """List notification logs"""
    queryset = NotificationLog.objects.all()
    serializer_class = NotificationLogSerializer
    permission_classes = [IsAdmin]

    def get_queryset(self):
        queryset = NotificationLog.objects.select_related('notification').all()

        # Filter by channel
        channel = self.request.query_params.get('channel')
        if channel:
            queryset = queryset.filter(channel=channel)

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by date
        date_from = self.request.query_params.get('date_from')
        if date_from:
            queryset = queryset.filter(sent_at__gte=date_from)

        return queryset.order_by('-sent_at')


@api_view(['POST'])
@permission_classes([IsAdmin])
def test_notification_system(request):
    """Test notification system by sending a test notification"""
    recipient_id = request.data.get('recipient_id')

    if not recipient_id:
        return Response({
            'error': 'Recipient ID is required.'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        recipient = User.objects.get(id=recipient_id)
    except User.DoesNotExist:
        return Response({
            'error': 'Recipient not found.'
        }, status=status.HTTP_404_NOT_FOUND)

    # Create test notification
    notification = Notification.objects.create(
        recipient=recipient,
        title='Test Notification',
        message='This is a test notification to verify the system is working correctly.',
        notification_type='system_announcement',
        priority='low',
        send_email=True,
        send_push=True
    )

    return Response({
        'message': 'Test notification sent successfully.',
        'notification': NotificationSerializer(notification).data
    }, status=status.HTTP_201_CREATED)
