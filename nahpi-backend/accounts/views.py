from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate, login
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
import random
import string

from .models import User, Department
from .serializers import (
    StudentRegistrationSerializer, UserSerializer, CustomTokenObtainPairSerializer,
    AdminLoginSerializer, DepartmentOfficerLoginSerializer, PasswordResetRequestSerializer,
    PasswordResetConfirmSerializer, EmailVerificationSerializer, PhoneVerificationSerializer,
    ChangePasswordSerializer, DepartmentSerializer
)


class StudentRegistrationView(generics.CreateAPIView):
    """Student registration endpoint"""
    queryset = User.objects.all()
    serializer_class = StudentRegistrationSerializer
    permission_classes = [permissions.AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Generate email verification token
        self.send_email_verification(user)

        # Generate phone verification code
        self.send_phone_verification(user)

        return Response({
            'message': 'Registration successful. Please check your email and phone for verification.',
            'user_id': user.id,
            'email': user.email,
            'phone_number': user.phone_number
        }, status=status.HTTP_201_CREATED)

    def send_email_verification(self, user):
        """Send email verification token"""
        token = default_token_generator.make_token(user)
        user.email_verification_token = token
        user.save(update_fields=['email_verification_token'])

        # In production, send actual email
        # For now, we'll just print to console
        verification_url = f"{settings.CORS_ALLOWED_ORIGINS[0]}/verify-email?token={token}&user_id={user.id}"

        try:
            send_mail(
                subject='Verify Your Email - NAHPi Complains',
                message=f'Please click the following link to verify your email: {verification_url}',
                from_email=settings.EMAIL_HOST_USER,
                recipient_list=[user.email],
                fail_silently=False,
            )
        except Exception as e:
            print(f"Email sending failed: {e}")
            print(f"Verification URL: {verification_url}")

    def send_phone_verification(self, user):
        """Send phone verification code"""
        code = ''.join(random.choices(string.digits, k=6))
        user.phone_verification_code = code
        user.save(update_fields=['phone_verification_code'])

        # In production, send actual SMS
        # For now, we'll just print to console
        print(f"Phone verification code for {user.phone_number}: {code}")


class CustomTokenObtainPairView(TokenObtainPairView):
    """Custom JWT token view with user data"""
    serializer_class = CustomTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)

        if response.status_code == 200:
            # Update last login
            email = request.data.get('email')
            if email:
                try:
                    user = User.objects.get(email=email)
                    user.last_login_at = timezone.now()
                    user.save(update_fields=['last_login_at'])
                except User.DoesNotExist:
                    pass

        return response


class AdminLoginView(APIView):
    """Admin login endpoint"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = AdminLoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.validated_data['user']

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access = refresh.access_token

        # Add custom claims
        access['role'] = user.role
        access['is_verified'] = user.is_verified

        # Update last login
        user.last_login_at = timezone.now()
        user.save(update_fields=['last_login_at'])

        return Response({
            'access': str(access),
            'refresh': str(refresh),
            'user': UserSerializer(user).data
        }, status=status.HTTP_200_OK)


class DepartmentOfficerLoginView(APIView):
    """Department officer login endpoint"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = DepartmentOfficerLoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.validated_data['user']

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access = refresh.access_token

        # Add custom claims
        access['role'] = user.role
        access['is_verified'] = user.is_verified

        # Update last login
        user.last_login_at = timezone.now()
        user.save(update_fields=['last_login_at'])

        return Response({
            'access': str(access),
            'refresh': str(refresh),
            'user': UserSerializer(user).data
        }, status=status.HTTP_200_OK)


class EmailVerificationView(APIView):
    """Email verification endpoint"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = EmailVerificationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        token = serializer.validated_data['token']
        user_id = request.data.get('user_id')

        try:
            user = User.objects.get(id=user_id)

            if user.email_verification_token == token:
                user.email_verified = True
                user.email_verification_token = None

                # Check if both email and phone are verified
                if user.phone_verified:
                    user.is_verified = True
                    user.verification_status = 'verified'

                user.save()

                return Response({
                    'message': 'Email verified successfully.',
                    'is_fully_verified': user.is_verified
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'error': 'Invalid verification token.'
                }, status=status.HTTP_400_BAD_REQUEST)

        except User.DoesNotExist:
            return Response({
                'error': 'Invalid user.'
            }, status=status.HTTP_400_BAD_REQUEST)


class PhoneVerificationView(APIView):
    """Phone verification endpoint"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = PhoneVerificationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        code = serializer.validated_data['code']
        user = request.user

        if user.phone_verification_code == code:
            user.phone_verified = True
            user.phone_verification_code = None

            # Check if both email and phone are verified
            if user.email_verified:
                user.is_verified = True
                user.verification_status = 'verified'

            user.save()

            return Response({
                'message': 'Phone verified successfully.',
                'is_fully_verified': user.is_verified
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'error': 'Invalid verification code.'
            }, status=status.HTTP_400_BAD_REQUEST)


class ResendPhoneVerificationView(APIView):
    """Resend phone verification code"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        user = request.user

        if user.phone_verified:
            return Response({
                'error': 'Phone number is already verified.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Generate new verification code
        code = ''.join(random.choices(string.digits, k=6))
        user.phone_verification_code = code
        user.save(update_fields=['phone_verification_code'])

        # In production, send actual SMS
        print(f"New phone verification code for {user.phone_number}: {code}")

        return Response({
            'message': 'Verification code sent successfully.'
        }, status=status.HTTP_200_OK)


class PasswordResetRequestView(APIView):
    """Password reset request endpoint"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = PasswordResetRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data['email']
        user = User.objects.get(email=email)

        # Generate password reset token
        token = default_token_generator.make_token(user)
        uidb64 = urlsafe_base64_encode(force_bytes(user.pk))

        # In production, send actual email
        reset_url = f"{settings.CORS_ALLOWED_ORIGINS[0]}/reset-password?token={token}&uidb64={uidb64}"

        try:
            send_mail(
                subject='Password Reset - NAHPi Complains',
                message=f'Please click the following link to reset your password: {reset_url}',
                from_email=settings.EMAIL_HOST_USER,
                recipient_list=[user.email],
                fail_silently=False,
            )
        except Exception as e:
            print(f"Email sending failed: {e}")
            print(f"Reset URL: {reset_url}")

        return Response({
            'message': 'Password reset link sent to your email.'
        }, status=status.HTTP_200_OK)


class PasswordResetConfirmView(APIView):
    """Password reset confirmation endpoint"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = PasswordResetConfirmSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        token = serializer.validated_data['token']
        password = serializer.validated_data['password']
        uidb64 = request.data.get('uidb64')

        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(pk=uid)

            if default_token_generator.check_token(user, token):
                user.set_password(password)
                user.save()

                return Response({
                    'message': 'Password reset successfully.'
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'error': 'Invalid or expired token.'
                }, status=status.HTTP_400_BAD_REQUEST)

        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            return Response({
                'error': 'Invalid token.'
            }, status=status.HTTP_400_BAD_REQUEST)


class ChangePasswordView(APIView):
    """Change password endpoint"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = ChangePasswordSerializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)

        user = request.user
        new_password = serializer.validated_data['new_password']

        user.set_password(new_password)
        user.save()

        return Response({
            'message': 'Password changed successfully.'
        }, status=status.HTTP_200_OK)


class UserProfileView(generics.RetrieveUpdateAPIView):
    """User profile endpoint"""
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user


class LogoutView(APIView):
    """Logout endpoint"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            refresh_token = request.data.get('refresh_token')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()

            return Response({
                'message': 'Logged out successfully.'
            }, status=status.HTTP_200_OK)
        except Exception:
            return Response({
                'error': 'Invalid token.'
            }, status=status.HTTP_400_BAD_REQUEST)


class DepartmentListView(generics.ListAPIView):
    """List all active departments"""
    queryset = Department.objects.filter(is_active=True)
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.AllowAny]


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_status(request):
    """Get current user status and verification info"""
    user = request.user

    return Response({
        'user': UserSerializer(user).data,
        'is_authenticated': True,
        'verification_status': {
            'email_verified': user.email_verified,
            'phone_verified': user.phone_verified,
            'is_fully_verified': user.is_verified
        }
    }, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def check_email_exists(request):
    """Check if email already exists"""
    email = request.data.get('email')

    if not email:
        return Response({
            'error': 'Email is required.'
        }, status=status.HTTP_400_BAD_REQUEST)

    exists = User.objects.filter(email=email).exists()

    return Response({
        'exists': exists
    }, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def check_student_id_exists(request):
    """Check if student ID already exists"""
    student_id = request.data.get('student_id')

    if not student_id:
        return Response({
            'error': 'Student ID is required.'
        }, status=status.HTTP_400_BAD_REQUEST)

    exists = User.objects.filter(student_id=student_id).exists()

    return Response({
        'exists': exists
    }, status=status.HTTP_200_OK)
