from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate, login
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
import random
import string

from .models import User, Department
from .serializers import (
    StudentRegistrationSerializer, UserSerializer, CustomTokenObtainPairSerializer,
    AdminLoginSerializer, DepartmentOfficerLoginSerializer, PasswordResetRequestSerializer,
    PasswordResetConfirmSerializer, EmailVerificationSerializer, PhoneVerificationSerializer,
    ChangePasswordSerializer, DepartmentSerializer, AdminUserCreateSerializer,
    AdminUserUpdateSerializer, UserListSerializer, DepartmentCreateUpdateSerializer
)
from .permissions import IsAdmin, IsAdminOrDepartmentOfficer, CanManageUsers


class StudentRegistrationView(generics.CreateAPIView):
    """Student registration endpoint"""
    queryset = User.objects.all()
    serializer_class = StudentRegistrationSerializer
    permission_classes = [permissions.AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Generate email verification token
        self.send_email_verification(user)

        # Generate phone verification code
        self.send_phone_verification(user)

        return Response({
            'message': 'Registration successful. Please check your email and phone for verification.',
            'user_id': user.id,
            'email': user.email,
            'phone_number': user.phone_number
        }, status=status.HTTP_201_CREATED)

    def send_email_verification(self, user):
        """Send email verification token"""
        token = default_token_generator.make_token(user)
        user.email_verification_token = token
        user.save(update_fields=['email_verification_token'])

        # In production, send actual email
        # For now, we'll just print to console
        verification_url = f"{settings.CORS_ALLOWED_ORIGINS[0]}/verify-email?token={token}&user_id={user.id}"

        try:
            send_mail(
                subject='Verify Your Email - NAHPi Complains',
                message=f'Please click the following link to verify your email: {verification_url}',
                from_email=settings.EMAIL_HOST_USER,
                recipient_list=[user.email],
                fail_silently=False,
            )
        except Exception as e:
            print(f"Email sending failed: {e}")
            print(f"Verification URL: {verification_url}")

    def send_phone_verification(self, user):
        """Send phone verification code"""
        code = ''.join(random.choices(string.digits, k=6))
        user.phone_verification_code = code
        user.save(update_fields=['phone_verification_code'])

        # In production, send actual SMS
        # For now, we'll just print to console
        print(f"Phone verification code for {user.phone_number}: {code}")


class CustomTokenObtainPairView(TokenObtainPairView):
    """Custom JWT token view with user data"""
    serializer_class = CustomTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)

        if response.status_code == 200:
            # Update last login
            email = request.data.get('email')
            if email:
                try:
                    user = User.objects.get(email=email)
                    user.last_login_at = timezone.now()
                    user.save(update_fields=['last_login_at'])
                except User.DoesNotExist:
                    pass

        return response


class AdminLoginView(APIView):
    """Admin login endpoint"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = AdminLoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.validated_data['user']

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access = refresh.access_token

        # Add custom claims
        access['role'] = user.role
        access['is_verified'] = user.is_verified

        # Update last login
        user.last_login_at = timezone.now()
        user.save(update_fields=['last_login_at'])

        return Response({
            'access': str(access),
            'refresh': str(refresh),
            'user': UserSerializer(user).data
        }, status=status.HTTP_200_OK)


class DepartmentOfficerLoginView(APIView):
    """Department officer login endpoint"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = DepartmentOfficerLoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.validated_data['user']

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access = refresh.access_token

        # Add custom claims
        access['role'] = user.role
        access['is_verified'] = user.is_verified

        # Update last login
        user.last_login_at = timezone.now()
        user.save(update_fields=['last_login_at'])

        return Response({
            'access': str(access),
            'refresh': str(refresh),
            'user': UserSerializer(user).data
        }, status=status.HTTP_200_OK)


class EmailVerificationView(APIView):
    """Email verification endpoint"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = EmailVerificationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        token = serializer.validated_data['token']
        user_id = request.data.get('user_id')

        try:
            user = User.objects.get(id=user_id)

            if user.email_verification_token == token:
                user.email_verified = True
                user.email_verification_token = None

                # Check if both email and phone are verified
                if user.phone_verified:
                    user.is_verified = True
                    user.verification_status = 'verified'

                user.save()

                return Response({
                    'message': 'Email verified successfully.',
                    'is_fully_verified': user.is_verified
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'error': 'Invalid verification token.'
                }, status=status.HTTP_400_BAD_REQUEST)

        except User.DoesNotExist:
            return Response({
                'error': 'Invalid user.'
            }, status=status.HTTP_400_BAD_REQUEST)


class PhoneVerificationView(APIView):
    """Phone verification endpoint"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = PhoneVerificationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        code = serializer.validated_data['code']
        user = request.user

        if user.phone_verification_code == code:
            user.phone_verified = True
            user.phone_verification_code = None

            # Check if both email and phone are verified
            if user.email_verified:
                user.is_verified = True
                user.verification_status = 'verified'

            user.save()

            return Response({
                'message': 'Phone verified successfully.',
                'is_fully_verified': user.is_verified
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'error': 'Invalid verification code.'
            }, status=status.HTTP_400_BAD_REQUEST)


class ResendPhoneVerificationView(APIView):
    """Resend phone verification code"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        user = request.user

        if user.phone_verified:
            return Response({
                'error': 'Phone number is already verified.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Generate new verification code
        code = ''.join(random.choices(string.digits, k=6))
        user.phone_verification_code = code
        user.save(update_fields=['phone_verification_code'])

        # In production, send actual SMS
        print(f"New phone verification code for {user.phone_number}: {code}")

        return Response({
            'message': 'Verification code sent successfully.'
        }, status=status.HTTP_200_OK)


class PasswordResetRequestView(APIView):
    """Password reset request endpoint"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = PasswordResetRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data['email']
        user = User.objects.get(email=email)

        # Generate password reset token
        token = default_token_generator.make_token(user)
        uidb64 = urlsafe_base64_encode(force_bytes(user.pk))

        # In production, send actual email
        reset_url = f"{settings.CORS_ALLOWED_ORIGINS[0]}/reset-password?token={token}&uidb64={uidb64}"

        try:
            send_mail(
                subject='Password Reset - NAHPi Complains',
                message=f'Please click the following link to reset your password: {reset_url}',
                from_email=settings.EMAIL_HOST_USER,
                recipient_list=[user.email],
                fail_silently=False,
            )
        except Exception as e:
            print(f"Email sending failed: {e}")
            print(f"Reset URL: {reset_url}")

        return Response({
            'message': 'Password reset link sent to your email.'
        }, status=status.HTTP_200_OK)


class PasswordResetConfirmView(APIView):
    """Password reset confirmation endpoint"""
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        serializer = PasswordResetConfirmSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        token = serializer.validated_data['token']
        password = serializer.validated_data['password']
        uidb64 = request.data.get('uidb64')

        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(pk=uid)

            if default_token_generator.check_token(user, token):
                user.set_password(password)
                user.save()

                return Response({
                    'message': 'Password reset successfully.'
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'error': 'Invalid or expired token.'
                }, status=status.HTTP_400_BAD_REQUEST)

        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            return Response({
                'error': 'Invalid token.'
            }, status=status.HTTP_400_BAD_REQUEST)


class ChangePasswordView(APIView):
    """Change password endpoint"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = ChangePasswordSerializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)

        user = request.user
        new_password = serializer.validated_data['new_password']

        user.set_password(new_password)
        user.save()

        return Response({
            'message': 'Password changed successfully.'
        }, status=status.HTTP_200_OK)


class UserProfileView(generics.RetrieveUpdateAPIView):
    """User profile endpoint"""
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user


class LogoutView(APIView):
    """Logout endpoint"""
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            refresh_token = request.data.get('refresh_token')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()

            return Response({
                'message': 'Logged out successfully.'
            }, status=status.HTTP_200_OK)
        except Exception:
            return Response({
                'error': 'Invalid token.'
            }, status=status.HTTP_400_BAD_REQUEST)


class DepartmentListView(generics.ListAPIView):
    """List all active departments"""
    queryset = Department.objects.filter(is_active=True)
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.AllowAny]


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_status(request):
    """Get current user status and verification info"""
    user = request.user

    return Response({
        'user': UserSerializer(user).data,
        'is_authenticated': True,
        'verification_status': {
            'email_verified': user.email_verified,
            'phone_verified': user.phone_verified,
            'is_fully_verified': user.is_verified
        }
    }, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def check_email_exists(request):
    """Check if email already exists"""
    email = request.data.get('email')

    if not email:
        return Response({
            'error': 'Email is required.'
        }, status=status.HTTP_400_BAD_REQUEST)

    exists = User.objects.filter(email=email).exists()

    return Response({
        'exists': exists
    }, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def check_student_id_exists(request):
    """Check if student ID already exists"""
    student_id = request.data.get('student_id')

    if not student_id:
        return Response({
            'error': 'Student ID is required.'
        }, status=status.HTTP_400_BAD_REQUEST)

    exists = User.objects.filter(student_id=student_id).exists()

    return Response({
        'exists': exists
    }, status=status.HTTP_200_OK)


# ============================================================================
# ADMIN USER MANAGEMENT VIEWS
# ============================================================================

class AdminUserListView(generics.ListAPIView):
    """Admin view to list all users with filtering and search"""
    queryset = User.objects.all()
    serializer_class = UserListSerializer
    permission_classes = [CanManageUsers]

    def get_queryset(self):
        from django.db import models
        queryset = User.objects.all().select_related('department')

        # Filter by role
        role = self.request.query_params.get('role')
        if role:
            queryset = queryset.filter(role=role)

        # Filter by department
        department = self.request.query_params.get('department')
        if department:
            queryset = queryset.filter(department__code=department)

        # Filter by verification status
        is_verified = self.request.query_params.get('is_verified')
        if is_verified is not None:
            queryset = queryset.filter(is_verified=is_verified.lower() == 'true')

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        # Search by name or email
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                models.Q(first_name__icontains=search) |
                models.Q(last_name__icontains=search) |
                models.Q(email__icontains=search) |
                models.Q(student_id__icontains=search)
            )

        return queryset.order_by('-created_at')


class AdminUserCreateView(generics.CreateAPIView):
    """Admin view to create new users"""
    queryset = User.objects.all()
    serializer_class = AdminUserCreateSerializer
    permission_classes = [CanManageUsers]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        return Response({
            'message': 'User created successfully.',
            'user': UserSerializer(user).data
        }, status=status.HTTP_201_CREATED)


class AdminUserDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Admin view to retrieve, update, or delete a user"""
    queryset = User.objects.all()
    permission_classes = [CanManageUsers]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return AdminUserUpdateSerializer
        return UserSerializer

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        return Response({
            'message': 'User updated successfully.',
            'user': UserSerializer(user).data
        }, status=status.HTTP_200_OK)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()

        # Prevent deletion of the current admin user
        if instance == request.user:
            return Response({
                'error': 'You cannot delete your own account.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Soft delete by deactivating the user
        instance.is_active = False
        instance.save()

        return Response({
            'message': 'User deactivated successfully.'
        }, status=status.HTTP_200_OK)


class AdminDepartmentListView(generics.ListCreateAPIView):
    """Admin view to list and create departments"""
    queryset = Department.objects.all()
    permission_classes = [CanManageUsers]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return DepartmentCreateUpdateSerializer
        return DepartmentSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        department = serializer.save()

        return Response({
            'message': 'Department created successfully.',
            'department': DepartmentSerializer(department).data
        }, status=status.HTTP_201_CREATED)


class AdminDepartmentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Admin view to retrieve, update, or delete a department"""
    queryset = Department.objects.all()
    permission_classes = [CanManageUsers]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return DepartmentCreateUpdateSerializer
        return DepartmentSerializer

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        department = serializer.save()

        return Response({
            'message': 'Department updated successfully.',
            'department': DepartmentSerializer(department).data
        }, status=status.HTTP_200_OK)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()

        # Check if department has users
        if instance.user_set.exists():
            return Response({
                'error': 'Cannot delete department with existing users. Please reassign users first.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if department has complaints
        if hasattr(instance, 'complaints') and instance.complaints.exists():
            return Response({
                'error': 'Cannot delete department with existing complaints.'
            }, status=status.HTTP_400_BAD_REQUEST)

        instance.delete()

        return Response({
            'message': 'Department deleted successfully.'
        }, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([CanManageUsers])
def admin_reset_user_password(request, user_id):
    """Admin endpoint to reset user password"""
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return Response({
            'error': 'User not found.'
        }, status=status.HTTP_404_NOT_FOUND)

    new_password = request.data.get('new_password')
    if not new_password:
        return Response({
            'error': 'New password is required.'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Validate password
    try:
        from django.contrib.auth.password_validation import validate_password
        validate_password(new_password, user)
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_400_BAD_REQUEST)

    user.set_password(new_password)
    user.save()

    return Response({
        'message': 'Password reset successfully.'
    }, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([CanManageUsers])
def admin_toggle_user_status(request, user_id):
    """Admin endpoint to activate/deactivate user"""
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return Response({
            'error': 'User not found.'
        }, status=status.HTTP_404_NOT_FOUND)

    # Prevent deactivating the current admin user
    if user == request.user:
        return Response({
            'error': 'You cannot deactivate your own account.'
        }, status=status.HTTP_400_BAD_REQUEST)

    user.is_active = not user.is_active
    user.save()

    status_text = 'activated' if user.is_active else 'deactivated'

    return Response({
        'message': f'User {status_text} successfully.',
        'is_active': user.is_active
    }, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([CanManageUsers])
def admin_verify_user(request, user_id):
    """Admin endpoint to manually verify user"""
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return Response({
            'error': 'User not found.'
        }, status=status.HTTP_404_NOT_FOUND)

    user.is_verified = True
    user.email_verified = True
    user.phone_verified = True
    user.verification_status = 'verified'
    user.save()

    return Response({
        'message': 'User verified successfully.',
        'user': UserSerializer(user).data
    }, status=status.HTTP_200_OK)


# ============================================================================
# ANALYTICS AND REPORTING VIEWS
# ============================================================================

@api_view(['GET'])
@permission_classes([IsAdminOrDepartmentOfficer])
def user_analytics(request):
    """Get user analytics and statistics"""
    from django.db.models import Count, Q
    from datetime import datetime, timedelta

    # Basic user counts
    total_users = User.objects.count()
    active_users = User.objects.filter(is_active=True).count()
    verified_users = User.objects.filter(is_verified=True).count()

    # Users by role
    users_by_role = User.objects.values('role').annotate(count=Count('id'))

    # Users by department
    users_by_department = User.objects.filter(department__isnull=False).values(
        'department__name', 'department__code'
    ).annotate(count=Count('id'))

    # Recent registrations (last 30 days)
    thirty_days_ago = datetime.now() - timedelta(days=30)
    recent_registrations = User.objects.filter(
        created_at__gte=thirty_days_ago
    ).count()

    # Verification statistics
    verification_stats = {
        'email_verified': User.objects.filter(email_verified=True).count(),
        'phone_verified': User.objects.filter(phone_verified=True).count(),
        'fully_verified': User.objects.filter(is_verified=True).count(),
        'pending_verification': User.objects.filter(is_verified=False).count(),
    }

    # Monthly registration trends (last 12 months)
    monthly_registrations = []
    for i in range(12):
        month_start = datetime.now().replace(day=1) - timedelta(days=30*i)
        month_end = month_start.replace(day=28) + timedelta(days=4)
        month_end = month_end - timedelta(days=month_end.day)

        count = User.objects.filter(
            created_at__gte=month_start,
            created_at__lte=month_end
        ).count()

        monthly_registrations.append({
            'month': month_start.strftime('%Y-%m'),
            'count': count
        })

    return Response({
        'total_users': total_users,
        'active_users': active_users,
        'verified_users': verified_users,
        'users_by_role': list(users_by_role),
        'users_by_department': list(users_by_department),
        'recent_registrations': recent_registrations,
        'verification_stats': verification_stats,
        'monthly_registrations': monthly_registrations[::-1]  # Reverse to show oldest first
    }, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsAdminOrDepartmentOfficer])
def department_analytics(request):
    """Get department analytics and statistics"""
    from django.db.models import Count

    departments = Department.objects.annotate(
        user_count=Count('user_set'),
        student_count=Count('user_set', filter=Q(user_set__role='student')),
        officer_count=Count('user_set', filter=Q(user_set__role='department_officer'))
    ).values(
        'id', 'name', 'code', 'is_active',
        'user_count', 'student_count', 'officer_count'
    )

    return Response({
        'departments': list(departments)
    }, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([CanManageUsers])
def admin_dashboard_stats(request):
    """Get comprehensive dashboard statistics for admin"""
    from django.db.models import Count, Q
    from datetime import datetime, timedelta

    # Get basic counts
    stats = {
        'users': {
            'total': User.objects.count(),
            'active': User.objects.filter(is_active=True).count(),
            'verified': User.objects.filter(is_verified=True).count(),
            'students': User.objects.filter(role='student').count(),
            'department_officers': User.objects.filter(role='department_officer').count(),
            'admins': User.objects.filter(role='admin').count(),
        },
        'departments': {
            'total': Department.objects.count(),
            'active': Department.objects.filter(is_active=True).count(),
        }
    }

    # Recent activity (last 7 days)
    seven_days_ago = datetime.now() - timedelta(days=7)
    stats['recent_activity'] = {
        'new_registrations': User.objects.filter(created_at__gte=seven_days_ago).count(),
        'recent_logins': User.objects.filter(last_login_at__gte=seven_days_ago).count(),
    }

    # Top departments by user count
    top_departments = Department.objects.annotate(
        user_count=Count('user_set')
    ).order_by('-user_count')[:5].values('name', 'code', 'user_count')

    stats['top_departments'] = list(top_departments)

    return Response(stats, status=status.HTTP_200_OK)


# ============================================================================
# BULK OPERATIONS AND UTILITIES
# ============================================================================

@api_view(['POST'])
@permission_classes([CanManageUsers])
def bulk_create_users(request):
    """Bulk create users from CSV file"""
    from .utils import bulk_create_users_from_csv

    if 'csv_file' not in request.FILES:
        return Response({
            'error': 'CSV file is required.'
        }, status=status.HTTP_400_BAD_REQUEST)

    csv_file = request.FILES['csv_file']

    # Validate file type
    if not csv_file.name.endswith('.csv'):
        return Response({
            'error': 'File must be a CSV file.'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Process CSV file
    results = bulk_create_users_from_csv(csv_file, request.user)

    return Response({
        'message': f'Bulk user creation completed. {results["created"]} users created.',
        'results': results
    }, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([CanManageUsers])
def export_users(request):
    """Export users to CSV"""
    from django.http import HttpResponse
    from .utils import export_users_to_csv

    csv_data = export_users_to_csv()

    response = HttpResponse(csv_data, content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="users_export.csv"'

    return response


@api_view(['POST'])
@permission_classes([CanManageUsers])
def send_bulk_notification(request):
    """Send bulk notification to users"""
    from .utils import send_bulk_notification_email

    # Get request data
    user_ids = request.data.get('user_ids', [])
    subject = request.data.get('subject')
    message = request.data.get('message')

    if not user_ids:
        return Response({
            'error': 'User IDs are required.'
        }, status=status.HTTP_400_BAD_REQUEST)

    if not subject or not message:
        return Response({
            'error': 'Subject and message are required.'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Get users
    users = User.objects.filter(id__in=user_ids, is_active=True)

    if not users.exists():
        return Response({
            'error': 'No valid users found.'
        }, status=status.HTTP_400_BAD_REQUEST)

    # Send notifications
    success = send_bulk_notification_email(users, subject, message)

    if success:
        return Response({
            'message': f'Notification sent to {users.count()} users successfully.'
        }, status=status.HTTP_200_OK)
    else:
        return Response({
            'error': 'Failed to send notifications.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([CanManageUsers])
def generate_user_report_view(request):
    """Generate comprehensive user report"""
    from .utils import generate_user_report

    # Get filters from query parameters
    filters = {}
    if request.query_params.get('role'):
        filters['role'] = request.query_params.get('role')
    if request.query_params.get('department'):
        filters['department'] = request.query_params.get('department')
    if request.query_params.get('is_verified'):
        filters['is_verified'] = request.query_params.get('is_verified').lower() == 'true'
    if request.query_params.get('date_from'):
        filters['date_from'] = request.query_params.get('date_from')
    if request.query_params.get('date_to'):
        filters['date_to'] = request.query_params.get('date_to')

    report = generate_user_report(filters)

    return Response(report, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([CanManageUsers])
def cleanup_unverified_users_view(request):
    """Clean up unverified users older than specified days"""
    from .utils import cleanup_unverified_users

    days_old = request.data.get('days_old', 30)

    try:
        days_old = int(days_old)
        if days_old < 7:
            return Response({
                'error': 'Minimum cleanup age is 7 days.'
            }, status=status.HTTP_400_BAD_REQUEST)
    except ValueError:
        return Response({
            'error': 'Invalid days_old value.'
        }, status=status.HTTP_400_BAD_REQUEST)

    deleted_count = cleanup_unverified_users(days_old)

    return Response({
        'message': f'Cleaned up {deleted_count} unverified users older than {days_old} days.'
    }, status=status.HTTP_200_OK)
