from rest_framework import permissions


class IsStudent(permissions.BasePermission):
    """
    Permission class to check if user is a student
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'student'
        )


class IsAdmin(permissions.BasePermission):
    """
    Permission class to check if user is an admin
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'admin'
        )


class IsDepartmentOfficer(permissions.BasePermission):
    """
    Permission class to check if user is a department officer
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'department_officer'
        )


class IsAdminOrDepartmentOfficer(permissions.BasePermission):
    """
    Permission class to check if user is admin or department officer
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role in ['admin', 'department_officer']
        )


class IsVerifiedUser(permissions.BasePermission):
    """
    Permission class to check if user is verified (email and phone)
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.is_verified
        )


class IsOwnerOrAdminOrDepartmentOfficer(permissions.BasePermission):
    """
    Permission class to check if user is the owner of the object,
    or an admin, or a department officer
    """
    
    def has_object_permission(self, request, view, obj):
        # Check if user is admin or department officer
        if request.user.role in ['admin', 'department_officer']:
            return True
        
        # Check if user is the owner of the object
        if hasattr(obj, 'user'):
            return obj.user == request.user
        elif hasattr(obj, 'complainant'):
            return obj.complainant == request.user
        elif hasattr(obj, 'owner'):
            return obj.owner == request.user
        else:
            return obj == request.user


class IsComplaintOwnerOrStaff(permissions.BasePermission):
    """
    Permission class for complaint-specific access control
    """
    
    def has_object_permission(self, request, view, obj):
        # Admin can access all complaints
        if request.user.role == 'admin':
            return True
        
        # Department officers can access complaints in their department
        if request.user.role == 'department_officer':
            if hasattr(obj, 'department'):
                return obj.department == request.user.department
            return False
        
        # Students can only access their own complaints
        if request.user.role == 'student':
            if hasattr(obj, 'complainant'):
                return obj.complainant == request.user
            return False
        
        return False


class IsDepartmentOfficerForComplaint(permissions.BasePermission):
    """
    Permission class to check if department officer belongs to the complaint's department
    """
    
    def has_object_permission(self, request, view, obj):
        if request.user.role != 'department_officer':
            return False
        
        if hasattr(obj, 'department'):
            return obj.department == request.user.department
        
        return False


class CanManageUsers(permissions.BasePermission):
    """
    Permission class for user management (admin only)
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'admin'
        )


class CanViewReports(permissions.BasePermission):
    """
    Permission class for viewing reports and analytics
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role in ['admin', 'department_officer']
        )


class ReadOnlyOrIsAuthenticated(permissions.BasePermission):
    """
    Permission class that allows read-only access to unauthenticated users
    and full access to authenticated users
    """
    
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        return request.user and request.user.is_authenticated


class IsStudentAndVerified(permissions.BasePermission):
    """
    Permission class to check if user is a verified student
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.role == 'student' and
            request.user.is_verified
        )
