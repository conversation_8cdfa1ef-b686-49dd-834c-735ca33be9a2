from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

app_name = 'accounts'

urlpatterns = [
    # Authentication endpoints
    path('register/', views.StudentRegistrationView.as_view(), name='student_register'),
    path('login/', views.CustomTokenObtainPairView.as_view(), name='student_login'),
    path('login/admin/', views.AdminLoginView.as_view(), name='admin_login'),
    path('login/department/', views.DepartmentOfficerLoginView.as_view(), name='department_login'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # Verification endpoints
    path('verify-email/', views.EmailVerificationView.as_view(), name='verify_email'),
    path('verify-phone/', views.PhoneVerificationView.as_view(), name='verify_phone'),
    path('resend-phone-verification/', views.ResendPhoneVerificationView.as_view(), name='resend_phone_verification'),
    
    # Password management
    path('password-reset/', views.PasswordResetRequestView.as_view(), name='password_reset'),
    path('password-reset-confirm/', views.PasswordResetConfirmView.as_view(), name='password_reset_confirm'),
    path('change-password/', views.ChangePasswordView.as_view(), name='change_password'),
    
    # User profile
    path('profile/', views.UserProfileView.as_view(), name='user_profile'),
    path('status/', views.user_status, name='user_status'),
    
    # Utility endpoints
    path('check-email/', views.check_email_exists, name='check_email'),
    path('check-student-id/', views.check_student_id_exists, name='check_student_id'),
    path('departments/', views.DepartmentListView.as_view(), name='departments'),

    # Admin user management endpoints
    path('admin/users/', views.AdminUserListView.as_view(), name='admin_user_list'),
    path('admin/users/create/', views.AdminUserCreateView.as_view(), name='admin_user_create'),
    path('admin/users/<uuid:pk>/', views.AdminUserDetailView.as_view(), name='admin_user_detail'),
    path('admin/users/<uuid:user_id>/reset-password/', views.admin_reset_user_password, name='admin_reset_password'),
    path('admin/users/<uuid:user_id>/toggle-status/', views.admin_toggle_user_status, name='admin_toggle_status'),
    path('admin/users/<uuid:user_id>/verify/', views.admin_verify_user, name='admin_verify_user'),

    # Admin department management endpoints
    path('admin/departments/', views.AdminDepartmentListView.as_view(), name='admin_department_list'),
    path('admin/departments/<uuid:pk>/', views.AdminDepartmentDetailView.as_view(), name='admin_department_detail'),

    # Analytics and reporting endpoints
    path('analytics/users/', views.user_analytics, name='user_analytics'),
    path('analytics/departments/', views.department_analytics, name='department_analytics'),
    path('admin/dashboard/stats/', views.admin_dashboard_stats, name='admin_dashboard_stats'),

    # Bulk operations and utilities
    path('admin/bulk/create-users/', views.bulk_create_users, name='bulk_create_users'),
    path('admin/export/users/', views.export_users, name='export_users'),
    path('admin/bulk/notification/', views.send_bulk_notification, name='send_bulk_notification'),
    path('admin/reports/users/', views.generate_user_report_view, name='generate_user_report'),
    path('admin/cleanup/unverified/', views.cleanup_unverified_users_view, name='cleanup_unverified_users'),
]
