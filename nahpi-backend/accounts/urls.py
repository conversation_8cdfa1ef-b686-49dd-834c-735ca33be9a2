from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

app_name = 'accounts'

urlpatterns = [
    # Authentication endpoints
    path('register/', views.StudentRegistrationView.as_view(), name='student_register'),
    path('login/', views.CustomTokenObtainPairView.as_view(), name='student_login'),
    path('login/admin/', views.AdminLoginView.as_view(), name='admin_login'),
    path('login/department/', views.DepartmentOfficerLoginView.as_view(), name='department_login'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # Verification endpoints
    path('verify-email/', views.EmailVerificationView.as_view(), name='verify_email'),
    path('verify-phone/', views.PhoneVerificationView.as_view(), name='verify_phone'),
    path('resend-phone-verification/', views.ResendPhoneVerificationView.as_view(), name='resend_phone_verification'),
    
    # Password management
    path('password-reset/', views.PasswordResetRequestView.as_view(), name='password_reset'),
    path('password-reset-confirm/', views.PasswordResetConfirmView.as_view(), name='password_reset_confirm'),
    path('change-password/', views.ChangePasswordView.as_view(), name='change_password'),
    
    # User profile
    path('profile/', views.UserProfileView.as_view(), name='user_profile'),
    path('status/', views.user_status, name='user_status'),
    
    # Utility endpoints
    path('check-email/', views.check_email_exists, name='check_email'),
    path('check-student-id/', views.check_student_id_exists, name='check_student_id'),
    path('departments/', views.DepartmentListView.as_view(), name='departments'),
]
