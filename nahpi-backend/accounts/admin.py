from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, Department, UserProfile


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Admin configuration for User model"""

    list_display = [
        'email', 'username', 'first_name', 'last_name', 'role',
        'is_verified', 'verification_status', 'created_at'
    ]
    list_filter = ['role', 'is_verified', 'verification_status', 'created_at']
    search_fields = ['email', 'username', 'first_name', 'last_name', 'student_id']
    ordering = ['-created_at']

    fieldsets = (
        (None, {'fields': ('email', 'username', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'phone_number')}),
        ('Role & Department', {'fields': ('role', 'department', 'student_id', 'level')}),
        ('Verification', {'fields': ('is_verified', 'verification_status', 'email_verified', 'phone_verified')}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login', 'date_joined', 'last_login_at')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'username', 'password1', 'password2', 'role'),
        }),
    )

    readonly_fields = ['created_at', 'last_login_at']


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    """Admin configuration for Department model"""

    list_display = ['name', 'code', 'head_of_department', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'code', 'description']
    ordering = ['name']

    fieldsets = (
        (None, {'fields': ('name', 'code', 'description')}),
        ('Contact Info', {'fields': ('email', 'phone_number')}),
        ('Management', {'fields': ('head_of_department', 'is_active')}),
    )

    readonly_fields = ['created_at', 'updated_at']


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """Admin configuration for UserProfile model"""

    list_display = ['user', 'date_of_birth', 'email_notifications', 'sms_notifications']
    list_filter = ['email_notifications', 'sms_notifications', 'created_at']
    search_fields = ['user__email', 'user__first_name', 'user__last_name']

    fieldsets = (
        ('User', {'fields': ('user',)}),
        ('Personal Info', {'fields': ('avatar', 'bio', 'date_of_birth', 'address')}),
        ('Emergency Contact', {'fields': ('emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship')}),
        ('Preferences', {'fields': ('email_notifications', 'sms_notifications')}),
    )

    readonly_fields = ['created_at', 'updated_at']
