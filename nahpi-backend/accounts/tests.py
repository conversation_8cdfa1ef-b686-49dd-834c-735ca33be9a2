from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from .models import Department, UserProfile
from .utils import generate_verification_code, create_admin_user

User = get_user_model()


class UserModelTest(TestCase):
    """Test User model functionality"""

    def setUp(self):
        self.department = Department.objects.create(
            name='Computer Science',
            code='CSC',
            description='Computer Science Department'
        )

    def test_create_student_user(self):
        """Test creating a student user"""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='student',
            password='testpass123',
            first_name='<PERSON>',
            last_name='<PERSON><PERSON>',
            phone_number='+2348012345678',
            role='student',
            student_id='NAH/2023/001234',
            department=self.department
        )

        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.role, 'student')
        self.assertTrue(user.is_student)
        self.assertFalse(user.is_admin)
        self.assertFalse(user.is_department_officer)
        self.assertEqual(user.department, self.department)

    def test_create_admin_user(self):
        """Test creating an admin user"""
        admin = create_admin_user(
            email='<EMAIL>',
            password='adminpass123',
            first_name='Admin',
            last_name='User'
        )

        self.assertEqual(admin.role, 'admin')
        self.assertTrue(admin.is_admin)
        self.assertTrue(admin.is_verified)
        self.assertTrue(admin.email_verified)
        self.assertTrue(admin.phone_verified)

    def test_user_full_name_property(self):
        """Test user full_name property"""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='test',
            password='testpass123',
            first_name='John',
            last_name='Doe'
        )

        self.assertEqual(user.full_name, 'John Doe')
