import random
import string
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes


def generate_verification_code(length=6):
    """Generate a random verification code"""
    return ''.join(random.choices(string.digits, k=length))


def generate_random_password(length=12):
    """Generate a random password"""
    characters = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(random.choices(characters, k=length))


def send_verification_email(user, verification_url):
    """Send email verification email"""
    subject = 'Verify Your Email - NAHPi Complains'
    
    # Create HTML content
    html_message = f"""
    <html>
    <body>
        <h2>Welcome to NAHPi Complains!</h2>
        <p>Hello {user.first_name},</p>
        <p>Thank you for registering with NAHPi Complains. Please click the button below to verify your email address:</p>
        <p>
            <a href="{verification_url}" 
               style="background-color: #08387F; color: white; padding: 12px 24px; 
                      text-decoration: none; border-radius: 4px; display: inline-block;">
                Verify Email Address
            </a>
        </p>
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p><a href="{verification_url}">{verification_url}</a></p>
        <p>This link will expire in 24 hours for security reasons.</p>
        <p>If you didn't create an account with us, please ignore this email.</p>
        <br>
        <p>Best regards,<br>NAHPi Complains Team</p>
    </body>
    </html>
    """
    
    # Create plain text version
    plain_message = f"""
    Welcome to NAHPi Complains!
    
    Hello {user.first_name},
    
    Thank you for registering with NAHPi Complains. Please click the link below to verify your email address:
    
    {verification_url}
    
    This link will expire in 24 hours for security reasons.
    
    If you didn't create an account with us, please ignore this email.
    
    Best regards,
    NAHPi Complains Team
    """
    
    try:
        send_mail(
            subject=subject,
            message=plain_message,
            html_message=html_message,
            from_email=settings.EMAIL_HOST_USER,
            recipient_list=[user.email],
            fail_silently=False,
        )
        return True
    except Exception as e:
        print(f"Failed to send verification email: {e}")
        return False


def send_password_reset_email(user, reset_url):
    """Send password reset email"""
    subject = 'Password Reset - NAHPi Complains'
    
    # Create HTML content
    html_message = f"""
    <html>
    <body>
        <h2>Password Reset Request</h2>
        <p>Hello {user.first_name},</p>
        <p>You have requested to reset your password for your NAHPi Complains account.</p>
        <p>Please click the button below to reset your password:</p>
        <p>
            <a href="{reset_url}" 
               style="background-color: #08387F; color: white; padding: 12px 24px; 
                      text-decoration: none; border-radius: 4px; display: inline-block;">
                Reset Password
            </a>
        </p>
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p><a href="{reset_url}">{reset_url}</a></p>
        <p>This link will expire in 1 hour for security reasons.</p>
        <p>If you didn't request a password reset, please ignore this email and your password will remain unchanged.</p>
        <br>
        <p>Best regards,<br>NAHPi Complains Team</p>
    </body>
    </html>
    """
    
    # Create plain text version
    plain_message = f"""
    Password Reset Request
    
    Hello {user.first_name},
    
    You have requested to reset your password for your NAHPi Complains account.
    
    Please click the link below to reset your password:
    
    {reset_url}
    
    This link will expire in 1 hour for security reasons.
    
    If you didn't request a password reset, please ignore this email and your password will remain unchanged.
    
    Best regards,
    NAHPi Complains Team
    """
    
    try:
        send_mail(
            subject=subject,
            message=plain_message,
            html_message=html_message,
            from_email=settings.EMAIL_HOST_USER,
            recipient_list=[user.email],
            fail_silently=False,
        )
        return True
    except Exception as e:
        print(f"Failed to send password reset email: {e}")
        return False


def send_sms_verification(phone_number, code):
    """Send SMS verification code (placeholder for SMS service integration)"""
    # In production, integrate with SMS service like Twilio, AWS SNS, etc.
    print(f"SMS to {phone_number}: Your NAHPi Complains verification code is: {code}")
    return True


def generate_password_reset_token(user):
    """Generate password reset token and uidb64"""
    token = default_token_generator.make_token(user)
    uidb64 = urlsafe_base64_encode(force_bytes(user.pk))
    return token, uidb64


def create_admin_user(email, password, first_name, last_name):
    """Create an admin user"""
    from .models import User
    
    admin = User.objects.create_user(
        email=email,
        username=email,
        password=password,
        first_name=first_name,
        last_name=last_name,
        role='admin',
        is_verified=True,
        email_verified=True,
        phone_verified=True,
        verification_status='verified'
    )
    return admin


def create_department_officer(email, password, first_name, last_name, department, phone_number):
    """Create a department officer user"""
    from .models import User, UserProfile
    
    officer = User.objects.create_user(
        email=email,
        username=email,
        password=password,
        first_name=first_name,
        last_name=last_name,
        phone_number=phone_number,
        department=department,
        role='department_officer',
        is_verified=True,
        email_verified=True,
        phone_verified=True,
        verification_status='verified'
    )
    
    # Create profile
    UserProfile.objects.create(user=officer)
    
    return officer


def validate_student_id_format(student_id):
    """Validate student ID format (customize based on your institution's format)"""
    # Example: NAH/2023/001234
    import re
    pattern = r'^NAH/\d{4}/\d{6}$'
    return re.match(pattern, student_id) is not None


def get_user_permissions(user):
    """Get user permissions based on role"""
    permissions = {
        'can_submit_complaints': False,
        'can_view_all_complaints': False,
        'can_manage_complaints': False,
        'can_view_reports': False,
        'can_manage_users': False,
        'can_manage_departments': False,
    }
    
    if user.role == 'student':
        permissions.update({
            'can_submit_complaints': user.is_verified,
        })
    elif user.role == 'department_officer':
        permissions.update({
            'can_view_all_complaints': True,
            'can_manage_complaints': True,
            'can_view_reports': True,
        })
    elif user.role == 'admin':
        permissions.update({
            'can_view_all_complaints': True,
            'can_manage_complaints': True,
            'can_view_reports': True,
            'can_manage_users': True,
            'can_manage_departments': True,
        })
    
    return permissions
