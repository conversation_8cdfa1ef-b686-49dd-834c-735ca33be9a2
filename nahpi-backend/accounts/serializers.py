from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import User, Department, UserProfile
import re


class DepartmentSerializer(serializers.ModelSerializer):
    """Serializer for Department model"""
    
    class Meta:
        model = Department
        fields = ['id', 'name', 'code', 'description', 'email', 'phone_number', 'is_active']
        read_only_fields = ['id']


class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for UserProfile model"""
    
    class Meta:
        model = UserProfile
        fields = [
            'avatar', 'bio', 'date_of_birth', 'address',
            'emergency_contact_name', 'emergency_contact_phone', 
            'emergency_contact_relationship', 'email_notifications', 
            'sms_notifications'
        ]


class UserSerializer(serializers.ModelSerializer):
    """Serializer for User model"""
    profile = UserProfileSerializer(read_only=True)
    department_name = serializers.CharField(source='department.name', read_only=True)
    full_name = serializers.CharField(read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name',
            'phone_number', 'role', 'is_verified', 'verification_status',
            'student_id', 'department', 'department_name', 'level',
            'email_verified', 'phone_verified', 'created_at', 'last_login_at',
            'profile'
        ]
        read_only_fields = [
            'id', 'is_verified', 'verification_status', 'email_verified', 
            'phone_verified', 'created_at', 'last_login_at'
        ]


class StudentRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for student registration"""
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    department_code = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'email', 'username', 'first_name', 'last_name', 'phone_number',
            'student_id', 'level', 'department_code', 'password', 'password_confirm'
        ]
    
    def validate_email(self, value):
        """Validate email format and uniqueness"""
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("A user with this email already exists.")
        return value
    
    def validate_phone_number(self, value):
        """Validate phone number format and uniqueness"""
        # Remove any non-digit characters except +
        cleaned_phone = re.sub(r'[^\d+]', '', value)
        
        if not re.match(r'^\+?1?\d{9,15}$', cleaned_phone):
            raise serializers.ValidationError(
                "Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
            )
        
        if User.objects.filter(phone_number=cleaned_phone).exists():
            raise serializers.ValidationError("A user with this phone number already exists.")
        
        return cleaned_phone
    
    def validate_student_id(self, value):
        """Validate student ID uniqueness"""
        if User.objects.filter(student_id=value).exists():
            raise serializers.ValidationError("A user with this student ID already exists.")
        return value
    
    def validate_department_code(self, value):
        """Validate department exists"""
        try:
            Department.objects.get(code=value.upper(), is_active=True)
        except Department.DoesNotExist:
            raise serializers.ValidationError("Invalid department code.")
        return value.upper()
    
    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Password confirmation does not match.")
        return attrs
    
    def create(self, validated_data):
        """Create new student user"""
        # Remove password_confirm and department_code from validated_data
        validated_data.pop('password_confirm')
        department_code = validated_data.pop('department_code')
        
        # Get department
        department = Department.objects.get(code=department_code)
        
        # Create user
        user = User.objects.create_user(
            email=validated_data['email'],
            username=validated_data['username'],
            password=validated_data['password'],
            first_name=validated_data['first_name'],
            last_name=validated_data['last_name'],
            phone_number=validated_data['phone_number'],
            student_id=validated_data['student_id'],
            level=validated_data['level'],
            department=department,
            role='student'
        )
        
        # Create user profile
        UserProfile.objects.create(user=user)
        
        return user


class AdminLoginSerializer(serializers.Serializer):
    """Serializer for admin login"""
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        """Validate admin credentials"""
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(email=email, password=password)
            
            if not user:
                raise serializers.ValidationError("Invalid email or password.")
            
            if not user.is_active:
                raise serializers.ValidationError("User account is disabled.")
            
            if user.role != 'admin':
                raise serializers.ValidationError("Access denied. Admin privileges required.")
            
            attrs['user'] = user
            return attrs
        
        raise serializers.ValidationError("Must include email and password.")


class DepartmentOfficerLoginSerializer(serializers.Serializer):
    """Serializer for department officer login"""
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        """Validate department officer credentials"""
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(email=email, password=password)
            
            if not user:
                raise serializers.ValidationError("Invalid email or password.")
            
            if not user.is_active:
                raise serializers.ValidationError("User account is disabled.")
            
            if user.role != 'department_officer':
                raise serializers.ValidationError("Access denied. Department officer privileges required.")
            
            attrs['user'] = user
            return attrs
        
        raise serializers.ValidationError("Must include email and password.")


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """Custom JWT token serializer with user data"""
    
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        
        # Add custom claims
        token['user_id'] = str(user.id)
        token['email'] = user.email
        token['role'] = user.role
        token['is_verified'] = user.is_verified
        
        return token
    
    def validate(self, attrs):
        """Validate user credentials and return tokens with user data"""
        data = super().validate(attrs)
        
        # Add user data to response
        user_serializer = UserSerializer(self.user)
        data['user'] = user_serializer.data
        
        return data


class PasswordResetRequestSerializer(serializers.Serializer):
    """Serializer for password reset request"""
    email = serializers.EmailField()
    
    def validate_email(self, value):
        """Validate email exists"""
        try:
            User.objects.get(email=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("No user found with this email address.")
        return value


class PasswordResetConfirmSerializer(serializers.Serializer):
    """Serializer for password reset confirmation"""
    token = serializers.CharField()
    password = serializers.CharField(validators=[validate_password])
    password_confirm = serializers.CharField()
    
    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Password confirmation does not match.")
        return attrs


class EmailVerificationSerializer(serializers.Serializer):
    """Serializer for email verification"""
    token = serializers.CharField()


class PhoneVerificationSerializer(serializers.Serializer):
    """Serializer for phone verification"""
    code = serializers.CharField(max_length=6)
    
    def validate_code(self, value):
        """Validate verification code format"""
        if not value.isdigit() or len(value) != 6:
            raise serializers.ValidationError("Verification code must be 6 digits.")
        return value


class ChangePasswordSerializer(serializers.Serializer):
    """Serializer for changing password"""
    old_password = serializers.CharField()
    new_password = serializers.CharField(validators=[validate_password])
    new_password_confirm = serializers.CharField()
    
    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("New password confirmation does not match.")
        return attrs
    
    def validate_old_password(self, value):
        """Validate old password"""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Old password is incorrect.")
        return value
